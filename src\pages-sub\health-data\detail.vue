<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { ref, watch } from 'vue'
import {
  getBloodOxygenRecordListAPI,
  getBloodPressureRecordListAPI,
  getBloodSugarRecordListAPI,
  getBodyFatRateRecordListAPI,
  getHeartRateRecordListAPI,
  getHeightWeightRecordListAPI,
  getRecentBloodOxygenRecordAPI,
  getRecentBloodPressureRecordAPI,
  getRecentBloodSugarRecordAPI,
  getRecentBodyFatRateRecordAPI,
  getRecentHeartRateRecordAPI,
  getRecentHeightWeightRecordAPI,
} from '@/api/health-data'
import FgNavbar from '@/components/fg-navbar/fg-navbar.vue'
// 1. 定义所有类型的数据
const healthDataMap = {
  bloodPressure: {
    recent: [
      { label: '收缩压', value: 99, unit: 'mmHg' },
      { label: '舒张压', value: 99, unit: 'mmHg' },
    ],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [
        { name: '收缩压', data: [99, 100, 98] },
        { name: '舒张压', data: [99, 98, 97] },
      ],
    },
  },
  bloodSugar: {
    recent: [{ label: '血糖', value: 4.1, unit: 'mmol/L' }],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [{ name: '血糖', data: [4.1, 4.3, 4.0] }],
    },
  },
  bloodOxygen: {
    recent: [
      { label: '动脉血氧', value: 98, unit: '%' },
      { label: '静脉血氧', value: 75, unit: '%' },
    ],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [
        { name: '动脉血氧', data: [98, 97, 99] },
        { name: '静脉血氧', data: [75, 76, 74] },
      ],
    },
  },
  heartRate: {
    recent: [{ label: '心率', value: 70, unit: '次/分' }],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [{ name: '心率', data: [70, 72, 69] }],
    },
  },
  bodyFatRate: {
    recent: [{ label: '体脂率', value: 70, unit: '%' }],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [{ name: '体脂率', data: [70, 71, 69] }],
    },
  },
  heightWeight: {
    recent: [{ label: '身高/体重', value: '175/59', unit: 'cm/kg' }],
    trend: {
      categories: ['05-12', '05-13', '05-14'],
      series: [
        { name: '身高', data: [175, 175, 175] },
        { name: '体重', data: [59, 59, 59] },
      ],
    },
  },
}

// 2. 定义响应式变量
const type = ref('')
const recentRecord = ref([]) // 最近记录
const trendData = ref({ categories: [], series: [] }) // 趋势图数据
const dayType = ref<'7' | '30'>('7') // 趋势图时间类型

// 3. 图表配置 - 根据天数动态配置
function getChartOpts() {
  const baseOpts = {
    color: [
      '#1890FF',
      '#91CB74',
      '#FAC858',
      '#EE6666',
      '#73C0DE',
      '#3CA272',
      '#FC8452',
      '#9A60B4',
      '#ea7ccc',
    ],
    padding: [15, 10, 0, 5], // 减少左侧padding从15到5
    animation: true, // 启用动画
    animationDuration: 300, // 动画持续时间300ms
    legend: {
      position: 'top',
      float: 'right',
      margin: 10,
    },
    yAxis: {
      gridType: 'dash',
      dashLength: 2,
    },
    extra: {
      line: {
        type: 'curve',
        width: 2,
        activeType: 'hollow',
      },
    },
  }

  // 30天数据启用滚动，7天数据不启用
  if (dayType.value === '30') {
    return {
      ...baseOpts,
      enableScroll: true,
      xAxis: {
        disableGrid: true,
        scrollShow: true,
        itemCount: 7, // 显示7个点，其余滚动查看
      },
    }
  }
  else {
    return {
      ...baseOpts,
      enableScroll: false,
      xAxis: {
        disableGrid: true,
        labelCount: 7,
      },
    }
  }
}

// 4. 加载数据
async function loadRecentRecord() {
  let res
  let res1
  switch (type.value) {
    case 'bloodPressure':
      res = await getRecentBloodPressureRecordAPI()
      res1 = await getBloodPressureRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '收缩压', value: res.data.systolic, unit: 'mmHg' },
          { label: '舒张压', value: res.data.diastolic, unit: 'mmHg' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '收缩压', data: list.map(item => item.systolic) },
            { name: '舒张压', data: list.map(item => item.diastolic) },
          ],
        }
      }
      break
    case 'bloodSugar':
      res = await getRecentBloodSugarRecordAPI()
      res1 = await getBloodSugarRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '血糖', value: res.data.glucose, unit: 'mmol/L' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '血糖', data: list.map(item => item.glucose) },
          ],
        }
      }
      break
    case 'heartRate':
      res = await getRecentHeartRateRecordAPI()
      res1 = await getHeartRateRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '心率', value: res.data.rate, unit: '次/分' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '心率', data: list.map(item => item.rate) },
          ],
        }
      }
      break
    case 'bodyFatRate':
      res = await getRecentBodyFatRateRecordAPI()
      res1 = await getBodyFatRateRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '体脂率', value: res.data.fatRate, unit: '%' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '体脂率', data: list.map(item => item.fatRate) },
          ],
        }
      }
      break
    case 'bloodOxygen':
      res = await getRecentBloodOxygenRecordAPI()
      res1 = await getBloodOxygenRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '动脉血氧', value: res.data.arterialOxygen, unit: '%' },
          { label: '静脉血氧', value: res.data.venousOxygen, unit: '%' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '动脉血氧', data: list.map(item => item.arterialOxygen) },
            { name: '静脉血氧', data: list.map(item => item.venousOxygen) },
          ],
        }
      }
      break
    case 'heightWeight':
      res = await getRecentHeightWeightRecordAPI()
      res1 = await getHeightWeightRecordListAPI({ type: dayType.value })
      if (res) {
        recentRecord.value = [
          { label: '身高', value: res.data.height, unit: 'cm' },
          { label: '体重', value: res.data.weight, unit: 'kg' },
        ]
      }
      if (res1 && Array.isArray(res1.data)) {
        const list = res1.data.slice(-Number(dayType.value))
        trendData.value = {
          categories: list.map(item => item.measureTime.slice(5, 10)),
          series: [
            { name: '身高', data: list.map(item => item.height) },
            { name: '体重', data: list.map(item => item.weight) },
          ],
        }
      }
      break
  }
}

// 5. 获取 type 参数
onLoad((options) => {
  type.value = options.type || ''
  loadRecentRecord()
  // 监听刷新事件
  uni.$on && uni.$on('refreshHealthData', loadRecentRecord)
})

// 页面卸载时移除监听，防止内存泄漏
onUnload(() => {
  uni.$off && uni.$off('refreshHealthData', loadRecentRecord)
})

// 6. 增加记录跳转
function onAddRecord() {
  uni.navigateTo({
    url: `/pages-sub/health-data/add_record?type=${type.value}`,
  })
}

// 7. 切换天数时自动刷新
watch(dayType, () => {
  loadRecentRecord()
})
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      数据详情
    </FgNavbar>
    <!-- 最近记录 -->
    <view class="recent-record">
      <view class="recent-record-title">
        最近记录
      </view>
      <view class="recent-record-content">
        <view v-if="recentRecord.length > 0" class="recent-record-item-group">
          <view
            v-for="item in recentRecord"
            :key="item.label"
            class="recent-record-item"
          >
            <view class="recent-record-item-value">
              {{ item.value }}
            </view>
            <view class="recent-record-item-label">
              {{ item.label }}{{ item.unit }}
            </view>
          </view>
        </view>
        <wd-status-tip
          v-else
          :image-size="{ height: 120, width: 120 }"
          image="/static/images/empty.png"
          tip="暂无记录"
        />
      </view>
    </view>
    <!-- 趋势图 -->
    <view class="trend-chart">
      <view class="trend-title-row">
        <view class="trend-title-left">
          变化趋势
        </view>
        <view class="trend-title-switch">
          <text :class="{ active: dayType === '7' }" @click="dayType = '7'">
            近7天
          </text>
          <text :class="{ active: dayType === '30' }" @click="dayType = '30'">
            近30天
          </text>
        </view>
      </view>
      <view v-if="trendData.series.length > 0 && trendData.series.some(s => s.data && s.data.length > 0)">
        <view class="custom-legend-row">
          <view class="custom-legend-right">
            <template v-for="(serie, idx) in trendData.series" :key="serie.name">
              <text
                class="legend-dot"
                :style="{
                  'color': getChartOpts().color[idx],
                  'margin-left': idx === 0 ? '0' : '16rpx',
                }"
              >
                ●
              </text>
              {{ serie.name }}
            </template>
          </view>
        </view>
        <view class="chart-container">
          <qiun-data-charts
            type="line"
            :chart-data="trendData"
            :opts="{ ...getChartOpts(), legend: { show: false } }"
            :ontouch="true"
            :animation="true"
            :canvas2d="true"
          />
        </view>
      </view>
      <wd-status-tip
        v-else
        :image-size="{ height: 120, width: 120 }"
        image="/static/images/empty.png"
        tip="暂无趋势图"
      />
    </view>
    <!-- 增加按钮 -->
    <view class="bottom-btn-bar">
      <view class="add-record-btn" @click="onAddRecord">
        增加记录
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  padding: 24rpx 32rpx;
  min-height: 90vh;
  .recent-record {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.03);
    .recent-record-title {
      font-size: 28rpx;
      font-family: 'PingFang SC';
      font-weight: 600;
      margin-bottom: 24rpx;
    }
    .recent-record-content {
      .recent-record-item-group {
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
      .recent-record-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        .recent-record-item-value {
          font-family: DIN;
          font-size: 40rpx;
          font-weight: 700;
          color: #222;
          margin-bottom: 8rpx;
        }
        .recent-record-item-label {
          font-family: 'PingFang SC';
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
  .trend-chart {
    background-color: #fff;
    border-radius: 16rpx;
    margin-top: 24rpx;
    padding: 24rpx;
    .trend-title-row {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      font-family: 'PingFang SC';
      font-weight: 600;
      margin-bottom: 0;
      .trend-title-left {
        font-size: 32rpx;
        font-weight: 700;
        color: #222;
        margin-right: 16rpx;
      }
      .trend-title-switch {
        display: flex;
        align-items: center;
        margin-left: auto;
        text {
          margin-left: 16rpx;
          color: #999;
          cursor: pointer;
          &.active {
            color: #1976d2;
            font-weight: bold;
          }
        }
      }
    }
    .custom-legend-row {
      display: flex;
      align-items: center;
      margin-top: 12px;
      margin-bottom: 12rpx;
      font-size: 24rpx;
      justify-content: flex-end;
      .custom-legend-right {
        display: flex;
        align-items: center;
        text {
          margin-left: 8rpx;
          color: #999;
        }
      }
    }
    .chart-container {
      width: 100%;
      height: 400rpx; /* 增加高度从300rpx到400rpx */
      /* 移除 overflow: hidden，让图表可以滚动 */
      position: relative;
    }
  }
  .bottom-btn-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 32rpx;
    display: flex;
    justify-content: center;
    z-index: 100;
    background: #fff;
    padding: 32rpx;
    .add-record-btn {
      width: 90vw;
      background: #008aff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      border: none;
      font-weight: bold;
      box-shadow: 0 4rpx 16rpx 0 rgba(0, 138, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 0;
    }
  }
}
</style>
