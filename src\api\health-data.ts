import { http } from '@/utils/http'
// 最近身高体重记录
export function getRecentHeightWeightRecordAPI() {
  return http.get('/h5/health/data/getLatestHeightWeight')
}
// 最近血压记录
export function getRecentBloodPressureRecordAPI() {
  return http.get('/h5/health/data/getLatestBloodPressure')
}
// 最近血糖记录
export function getRecentBloodSugarRecordAPI() {
  return http.get('/h5/health/data/getLatestBloodSugar')
}
// 最近血氧记录
export function getRecentBloodOxygenRecordAPI() {
  return http.get('/h5/health/data/getLatestBloodOxygen')
}
// 最近心率记录
export function getRecentHeartRateRecordAPI() {
  return http.get('/h5/health/data/getLatestHeartRate')
}
// 最近体脂率记录
export function getRecentBodyFatRateRecordAPI() {
  return http.get('/h5/health/data/getLatestBodyFat')
}
// 新增身高体重记录
export function addHeightWeightRecordAPI(data: any) {
  return http.post('/h5/health/data/addHeightWeight', data)
}
// 新增血压记录
export function addBloodPressureRecordAPI(data: any) {
  return http.post('/h5/health/data/addBloodPressure', data)
}
// 新增血糖记录
export function addBloodSugarRecordAPI(data: any) {
  return http.post('/h5/health/data/addBloodSugar', data)
}
// 新增血氧记录
export function addBloodOxygenRecordAPI(data: any) {
  return http.post('/h5/health/data/addBloodOxygen', data)
}
// 新增心率记录
export function addHeartRateRecordAPI(data: any) {
  return http.post('/h5/health/data/addHeartRate', data)
}
// 新增体脂率记录
export function addBodyFatRateRecordAPI(data: any) {
  return http.post('/h5/health/data/addBodyFat', data)
}

// 身高体重记录列表
export function getHeightWeightRecordListAPI(params: any) {
  return http.get('/h5/health/data/getHeightWeights', params)
}
// 血压记录列表
export function getBloodPressureRecordListAPI(params: any) {
  return http.get('/h5/health/data/getBloodPressures', params)
}
// 血糖记录列表
export function getBloodSugarRecordListAPI(params: any) {
  return http.get('/h5/health/data/getBloodSugars', params)
}
// 血氧记录列表
export function getBloodOxygenRecordListAPI(params: any) {
  return http.get('/h5/health/data/getBloodOxygens', params)
}
// 心率记录列表
export function getHeartRateRecordListAPI(params: any) {
  return http.get('/h5/health/data/getHeartRates', params)
}
// 体脂率记录列表
export function getBodyFatRateRecordListAPI(params: any) {
  return http.get('/h5/health/data/getBodyFats', params)
}

// 获取全部最近健康数据
export function getRecentHealthDataAPI() {
  return http.get('/h5/health/data/getHealthData')
}
