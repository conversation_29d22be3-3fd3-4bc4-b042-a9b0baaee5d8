<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: ' ',
  },
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'

import { useToast } from 'wot-design-uni'
import { get_feedback_type, post_feedback } from '@/api/feedback'
import { ossUpload } from '@/api/uploda'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'

const uploadBaseUrl = import.meta.env.VITE_UPLOAD_URL
const toast = useToast()
// code here
// const { success: showSuccess } = useToast()
const feedback_value = ref()
const columns = ref()
// get_feedback_type().then(res => {
//     console.log(res)
// })
const model = reactive<{
  type: string
  content: string
  images: []
}>({
  type: '',
  content: '',
  images: [],
})
function handleChange(val) {
  // console.log(val)
}
const form = ref()

// 提交按钮
function handleSubmit() {
  // console.log(form.value)
  //       console.log(model.images.map(item=>JSON.parse(item.response).data.fileName))
  form.value
    .validate()
    .then(({ valid, errors }) => {
      if (valid) {
        const data = { ...model, images: model.images.map((item: any) => JSON.parse(item.response).data.fileName).join(',') }
        // return
        post_feedback(data).then((res) => {
          toast.success(res.msg)
          setTimeout(() => {
            uni.navigateBack({
              delta: 1, // 返回的页面数
            })
          }, 1000)
        })
      }
      else {
        console.log(errors)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
onMounted(() => {
  get_feedback_type().then((res) => {
    console.log(res)
    columns.value = res.data
  })
})
function validatorMessage(val) {
  if (val) {
    return Promise.resolve()
  }
  else {
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject('请选择文章类型')
  }
}
function customUpload(file: any) {
  // return new Promise((resolve, reject) => {
  ossUpload(file.url).then(() => {
    // resolve({
    //   url: res, // 根据实际返回结构调整
    // })
  }).catch(() => {
    // reject(err)
  })
  // })
}
const ContentTypeEnum = {
  // form-data  upload
  FORM_DATA: 'multipart/form-data;charset=UTF-8',
  // form-data qs
  FORM_URLENCODED: 'application/x-www-form-urlencoded;charset=UTF-8',
  // json
  JSON: 'application/json;charset=UTF-8',
} as const
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      意见反馈
    </FgNavbar>
    <wd-form ref="form" :model="model">
      <view class="pt-[30px] text-[14px]">
        <view>
          现病史
        </view>
        <view style="border-bottom: 1px solid #d9d9d9;margin: 12px 0;">
          <wd-picker
            v-model="model.type" label="反馈类型"
            placeholder="请选择反馈类型"
            :rules="[{ required: false, validator: validatorMessage, message: '请选择反馈类型' }]" prop="type"
            label-width="100px" value-key="dictValue" label-key="dictLabel" :columns="columns"
          />
        </view>
        <view style="border-bottom: 1px solid #d9d9d9; margin: 12px 0;">
          <view>
            反馈描述
          </view>
          <wd-textarea
            v-model="model.content" :rules="[{ required: true, message: '请输入反馈内容' }]" prop="content"
            placeholder="请输入" custom-textarea-class="textarea_bg "
          />
        </view>
        <view class="mt-[12px]">
          <view>
            反馈截图
          </view>
          <view class="upload-content-item-upload upload">
            <!-- <wd-upload
              v-model:file-list="model.images"
              label="反馈截图" :limit="3"
              :upload-method="customUpload"
              @change="handleChange"
            /> -->

            <wd-upload
              v-model:file-list="model.images" label="反馈截图" :limit="3"
              :action="uploadBaseUrl" :header="{
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': 'XMLHttpRequest',
              }" @change="handleChange"
            />
          </view>
        </view>
      </view>
      <view class="footer">
        <view class="footer_btn" @click="handleSubmit">
          提交
        </view>
      </view>
    </wd-form>
  </view>
</template>

<style lang="scss" scoped>
// *{
//     box-sizing: border-box;
// }
.page {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.textarea_bg) {
  //   @apply page bg-[#f0f0f0] color-amber; /* 使用 Tailwind 类 */

  /* 或者直接写 CSS */
  background: #f0f0f0 !important;

  //   background-color: red !important;
  padding: 0.7rem !important;
}

.upload {
  background-color: #fff;
  padding: 32rpx 24rpx 0 32rpx;
}

.upload-content-item-upload {
  padding-left: 0 !important;
}

.footer {
  background: #fff;
  display: flex;
  padding: 16px;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  box-shadow: 0px -4px 4px 0px rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 3%;
  right: 0;
  width: 100%;
  box-sizing: border-box;

  .footer_btn {
    display: flex;
    height: 46px;
    padding: 12px 20px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    flex: 1 0 0;
    border-radius: 100px;
    background: #007fee;
    color: #fff;
    box-sizing: border-box;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
}
:deep(.wd-picker__cell) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
:deep(.wd-textarea) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>
