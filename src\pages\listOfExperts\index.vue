<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import { nextTick, ref } from 'vue'
import { getExpertLeftListAPI, getExpertrightListAPI } from '@/api/listOfExperts'
import CustomTabbar from '@/components/custom-tabbar/index.vue'
import { updateTabbarBadge } from '@/utils/updateTabbarBadge'

const active = ref<number>(0)
const scrollTop = ref<number>(0)
const BASE_URL = import.meta.env.VITE_IMG_URL
// 专家分类数据
const categories = ref([])

// 专家数据
const experts = ref([])

// 获取专家列表的参数
const pages = ref({
  name: '', // 专家姓名
  pageNum: 1, // 页码
  pageSize: 10, // 每页条数
  chronicDiseaseId: '', // 病种id，初始为空
})

//  点击分类
function handleChange({ value }) {
  active.value = value
  // 赋值当前选中分类的id
  pages.value.chronicDiseaseId = categories.value[value]?.title || ''
  getExpertrightList()
  scrollTop.value = -1
  nextTick(() => {
    scrollTop.value = 0
  })
}
// 点击专家
function handleClick(expert) {
  console.log(expert.id)
  //  跳转到专家主页
  uni.navigateTo({
    url: `/pages-sub/expertDetail/index?id=${expert.id}`,
  })
}
// 获取专家疾病列表
async function getExpertleftList() {
  const res = await getExpertLeftListAPI()
  categories.value = ((res.data as any[]) || []).map((item: any) => ({
    label: item.diseaseName,
    title: item.id,
    disabled: false,
  }))
  // 分类数据获取后再赋值
  if (categories.value.length > 0) {
    pages.value.chronicDiseaseId = categories.value[0].title
    getExpertrightList()
  }
}
// 搜索事件
function handleSearch(val) {
  pages.value.name = (val && val.value) || val || ''
  pages.value.pageNum = 1
  getExpertrightList()
}
// 加载更多
async function loadMore() {
  // 假设接口返回的总数在 res.total
  if (experts.value.length >= (experts as any).total)
    return
  pages.value.pageNum++
  await getExpertrightList(true)
}
// 修改专家列表获取，支持追加
async function getExpertrightList(append = false) {
  const res = await getExpertrightListAPI(pages.value)
  if (append) {
    experts.value = [...experts.value, ...(((res as any).rows) || [])]
  }
  else {
    experts.value = ((res as any).rows || []) as any;
    // 保存总数用于判断是否还有更多
    (experts as any).total = (res as any).total || 0
  }
}
// 是否需要拼接BASE_URL
function getAvatarUrl(avatar: string) {
  // 如果是 http/https 开头，直接返回
  if (/^https?:\/\//.test(avatar))
    return avatar
  // 否则拼接 BASE_URL
  return BASE_URL + avatar
}
onShow(() => {
  // 如果是首次加载（categories为空），则获取分类列表
  if (categories.value.length === 0) {
    getExpertleftList()
  }
  else {
    // 如果已有分类数据，保持当前选中状态，只刷新专家列表
    getExpertrightList()
  }
  // 每次显示页面时都更新红点状态
  updateTabbarBadge()
})
</script>

<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <FgNavbar :left-arrow="false" left-text="">
      专家列表
    </FgNavbar>
    <wd-search hide-cancel placeholder="请输入关键词" placeholder-left @search="handleSearch" @clear="handleSearch" />
    <view class="wrapper">
      <wd-sidebar v-model="active" @change="handleChange">
        <wd-sidebar-item
          v-for="(item, index) in categories" :key="index" :value="index" :label="item.label"
          :disabled="item.disabled"
        />
      </wd-sidebar>
      <!-- 内容 -->
      <view class="content" :style="`transform: translateY(-${active * 100}%)`">
        <scroll-view
          v-for="(category, index) in categories" :key="index" class="category" scroll-y
          scroll-with-animation :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
          @scrolltolower="loadMore"
        >
          <view v-for="expert in experts" :key="expert.id" class="expert-card" @click="handleClick(expert)">
            <view class="expert-header">
              <view class="expert-info">
                <img :src="getAvatarUrl(expert.avatar)" alt="" class="avatar">
                <view class="expert-details">
                  <view class="expert-name">
                    <text class="name">
                      {{ expert.name }}
                    </text>
                    <text class="title">
                      {{ expert.title }}
                    </text>
                  </view>
                  <view class="hospital-info">
                    <text class="hospital">
                      {{ expert.hospital }}
                    </text>
                    <text class="department">
                      {{ expert.department }}
                    </text>
                  </view>
                  <view class="consultation-time">
                    <text>接诊时间：{{ expert.startConsultationTime }} ~ {{ expert.endConsultationTime }}</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="expert-stats">
              <view class="stat-item">
                <text class="stat-label">
                  好评
                </text>
                <text class="stat-value">
                  {{ expert.score }}
                </text>
              </view>
              <view class="stat-item">
                <text class="stat-label">
                  月回答
                </text>
                <text class="stat-value">
                  {{ expert.replyNum }}
                </text>
              </view>
              <view class="stat-item">
                <text class="stat-label">
                  接诊总数
                </text>
                <text class="stat-value">
                  {{ expert.patientNum }}
                </text>
              </view>
            </view>

            <view class="expert-specialties">
              <view v-for="specialty in expert.chronicDiseases" :key="specialty" class="specialty-tag">
                {{ specialty.diseaseName }}
              </view>
            </view>
          </view>
          <template v-if="experts.length === 0">
            <view class="empty-tip">
              <wd-status-tip
                :image-size="{ height: 120, width: 120 }"
                image="/static/images/empty.png"
                tip="暂无专家"
              />
            </view>
          </template>
        </scroll-view>
      </view>
    </view>
    <!-- 自定义tabbar -->
    <CustomTabbar />
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  min-height: 100vh;

  .wrapper {
    margin: 16rpx 0;
    background-color: #f7f8f9;
    display: flex;
    height: calc(100vh - 120px - 200rpx); /* 减去导航栏高度和tabbar高度，增加更多空间 */
    height: calc(100vh - 120px - 200rpx - constant(safe-area-inset-bottom));
    height: calc(100vh - 120px - 200rpx - env(safe-area-inset-bottom));
    overflow: hidden;
  }

  .content {
    flex: 1;
    background: #f7f8f9;
    transition: transform 0.3s ease;
  }

  .category {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    background-color: #f7f8f9;
    display: flex;
    align-items: center;
    justify-content: center;

    .expert-card {
      background: #ffffff;
      padding: 32rpx;
      border-bottom: 1px solid #d9d9d9;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);

      .expert-header {
        margin-bottom: 24rpx;

        .expert-info {
          display: flex;
          align-items: flex-start;
          gap: 24rpx;

          .avatar {
            width: 112rpx;
            height: 112rpx;
            border-radius: 50%;
            flex-shrink: 0;
          }

          .expert-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .expert-name {
              display: flex;
              align-items: center;
              gap: 16rpx;

              .name {
                color: #333333;
                font-family: 'PingFang SC';
                font-size: 36rpx;
                font-weight: 600;
                line-height: 44rpx;
              }

              .title {
                color: #666666;
                font-family: 'PingFang SC';
                font-size: 28rpx;
                font-weight: 400;
                line-height: 32rpx;
              }
            }

            .hospital-info {
              display: flex;
              align-items: center;
              gap: 16rpx;

              .hospital {
                color: #333333;
                font-family: 'PingFang SC';
                font-size: 28rpx;
                font-weight: 400;
                line-height: 32rpx;
                max-width: 300rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .department {
                color: #666666;
                font-family: 'PingFang SC';
                font-size: 28rpx;
                font-weight: 400;
                line-height: 32rpx;
                flex-shrink: 0;
              }
            }

            .consultation-time {
              color: #999;
              font-family: 'PingFang SC';
              font-size: 28rpx;
              font-style: normal;
              font-weight: 400;
              line-height: 32rpx;
              /* 114.286% */
            }
          }
        }
      }

      .expert-stats {
        display: flex;
        justify-content: space-between;
        margin: 24rpx 0;

        .stat-item {
          align-items: center;
          gap: 4rpx;

          .stat-label {
            color: #999999;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-weight: 400;
            line-height: 28rpx;
          }

          .stat-value {
            color: #333;
            text-align: justify;
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 32rpx;
            /* 114.286% */
          }
        }

        .stat-item:first-child .stat-value {
          color: #ff9400;
          text-align: justify;
          font-family: 'PingFang SC';
          font-size: 28rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 32rpx;
        }
      }

      .expert-specialties {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .specialty-tag {
          background: #f5f5f5;
          color: #007fee;
          text-align: justify;
          font-family: 'PingFang SC';
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 40rpx;
          padding: 8rpx 16rpx;
          border-radius: 40rpx;
          background: #ebf5ff;
        }
      }
    }
  }
}

.wd-navbar {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
  height: 88rpx; // 或组件默认高度
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.empty-tip {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  z-index: 1;
}

// 自定义侧边栏未选中状态的颜色
:deep(.wd-sidebar-item) {
  color: rgba(0, 0, 0, 0.65) !important;
  font-size: 28rpx !important;
  justify-content: end !important;
}

// 自定义侧边栏选中状态的颜色（需要更高优先级）
:deep(.wd-sidebar-item--active) {
  color: #007fee !important;
}
</style>
