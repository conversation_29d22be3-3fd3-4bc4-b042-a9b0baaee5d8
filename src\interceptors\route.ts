/**
 * by 菲鸽 on 2024-03-06
 * 路由拦截，通常也是登录拦截
 * 可以设置路由白名单，或者黑名单，看业务需要选哪一个
 * 我这里应为大部分都可以随便进入，所以使用黑名单
 */
// import { useUserStore } from '@/store'
import { getLastPage } from '@/utils'

// TODO Check
const loginRoute = import.meta.env.VITE_LOGIN_URL
// 判断是否登录
function isLogined() {
  // 只要本地有 token 就算登录
  return !!uni.getStorageSync('token')
  // const userStore = useUserStore()
  // return !!userStore.userInfo.username
}

const isDev = import.meta.env.DEV

// 可选登录拦截器 - （适用于小程序审核，不强制登录）
const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  // 增加对相对路径的处理，BY 网友 @ideal
  invoke({ url }: { url: string }) {
    let path = url.split('?')[0]

    // 处理相对路径
    if (!path.startsWith('/')) {
      const currentPath = getLastPage().route // 获取当前页面路径
      const normalizedCurrentPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}` // 规范化当前路径
      const baseDir = normalizedCurrentPath.substring(0, normalizedCurrentPath.lastIndexOf('/')) // 获取当前页面路径的父级目录
      path = `${baseDir}/${path}` // 拼接路径
    }

    // 登录页面直接通过
    const loginPage = '/pages/login/index'
    if (path === loginPage) {
      return true
    }

    // 其他页面也直接通过，不强制登录
    // 如果需要登录的功能，可以在具体页面内部判断
    return true
  },
}
// 路由拦截器
export const routeInterceptor = {
  install() {
    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
    uni.addInterceptor('switchTab', navigateToInterceptor)
  },
}
