import { http } from '@/utils/http'

// 短信登录
export function smsLoginAPI(data: { phonenumber: string, smsCode: string }) {
  return http.post('/h5/member/login/sms', data)
}
// 短信验证码
export function smsCodeAPI(phonenumber: string) {
  return http.get('/resource/sms/code', { phonenumber })
}

// 微信手机号一键登录（新接口）
export function wxPhoneLoginAPI(data: {
  code: string
  encryptedData: string
  iv: string
  sessionKey: string
  openid: string
  unionid: string
}) {
  return http.post('/h5/member/login/wXPhoneLogin', data)
}

// code置换微信信息
export function getWxCodeAPI(data: { code: string }) {
  return http.post('/h5/member/login/code', data)
}

// 获取用户信息
export function getUserInfoAPI() {
  return http.get('/h5/member/getInfo')
}

// 协议-隐私
export function privacyPolicyAPI(type: string) {
  return http.get(`/h5/agreement/${type}`)
}
// 退出登录
export function logoutAPI() {
  return http.post('/h5/member/logout')
}
// 客户端管理
export function PCXCXmanagement(params) {
  return http.get('/system/client/getByClientId', params)
}
// 过审
export function getAuditAPI() {
  return http.get('/h5/member/login/getWxxcType')
}
