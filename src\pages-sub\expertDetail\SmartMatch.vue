<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { getSmartMatchAPI } from '@/api/listOfExperts'
import FgNavbar from '@/components/fg-navbar/fg-navbar.vue'

const BASE_URL = import.meta.env.VITE_IMG_URL
// 智能推荐列表
const smartMatchList = ref([])
const pages = ref({
  name: '',
  pageNum: 1,
  pageSize: 10,
})
// 搜索关键词
const value = ref<string>('')
// 搜索
function search() {
  console.log('搜索关键词:', value.value)
  // 更新搜索参数
  pages.value.name = value.value
  pages.value.pageNum = 1
  // 重新获取数据
  getSmartMatch()
}
// 重置
function clear() {
  console.log('清空搜索')
  // 清空搜索关键词
  value.value = ''
  pages.value.name = ''
  pages.value.pageNum = 1
  // 重新获取数据
  getSmartMatch()
}
// 智能推荐
async function getSmartMatch(append = false) {
  try {
    const res = await getSmartMatchAPI(pages.value)
    console.log('智能匹配结果:', res)

    if (append) {
      // 追加数据（加载更多）
      smartMatchList.value = [...smartMatchList.value, ...((res as any).rows || [])]
    }
    else {
      // 替换数据（搜索或首次加载）
      smartMatchList.value = (res as any).rows || []
    }

    // 保存总数用于判断是否还有更多
    (smartMatchList as any).total = (res as any).total || 0
  }
  catch (error) {
    console.error('获取智能匹配数据失败:', error)
    if (!append) {
      smartMatchList.value = []
    }
  }
}

// 加载更多
async function loadMore() {
  // 检查是否还有更多数据
  if (smartMatchList.value.length >= (smartMatchList as any).total) {
    return
  }

  pages.value.pageNum++
  await getSmartMatch(true)
}
// 点击专家
function handleClick(item: any) {
  console.log(item)
  uni.navigateTo({
    url: `/pages-sub/expertDetail/index?id=${item.id}`,
  })
}
onLoad(() => {
  getSmartMatch()
})
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      智能匹配
    </FgNavbar>
    <!-- 搜索框 -->
    <view class="search-box">
      <wd-search
        v-model="value" hide-cancel placeholder-left placeholder="请输入关键词" @search="search"
        @clear="clear"
      />
    </view>
    <!-- 专家列表 -->
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="loadMore"
    >
      <view v-for="item in smartMatchList" :key="item.id" class="expert-list" @click="handleClick(item)">
        <view class="expert-item">
          <!-- 专家信息 -->
          <view class="expert-item-top">
            <image class="expert-item-avatar" :src="item.avatar ? BASE_URL + item.avatar : '../../static/images/avatar.jpg'" />
            <view class="expert-item-info">
              <view class="row">
                <text class="name">
                  {{ item.name }}
                </text>
                <text class="title">
                  {{ item.title }}
                </text>
              </view>
              <view class="hospital">
                <text>{{ item.hospital }}</text>
                <text>{{ item.department }}</text>
              </view>
              <view class="time">
                <text>接诊时间：</text>
                <text>{{ item.startConsultationTime }}~ {{ item.endConsultationTime }}</text>
              </view>
            </view>
            <view class="expert-item-icon">
              <image src="@/static/svg/arrows.svg" />
            </view>
          </view>
          <!-- 统计评分 -->
          <view class="expert-item-stats">
            <view class="expert-item-stats-item">
              <text>好评</text>
              <text class="expert-item-stats-item-value">
                {{ item.score }}
              </text>
            </view>
            <view class="expert-item-stats-item">
              <text>月回答</text>
              <text>{{ item.replyNum }}</text>
            </view>
            <view class="expert-item-stats-item">
              <text>接诊总数</text>
              <text>{{ item.patientNum }}</text>
            </view>
          </view>
          <!-- 标签 -->
          <view class="expert-item-stats-tabs">
            <view v-for="tag in item.chronicDiseases" :key="tag.id" class="expert-item-stats-tabs-item">
              <text>{{ tag.diseaseName }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="smartMatchList.length === 0" class="empty-tip-wrapper">
        <wd-status-tip
          :image-size="{ height: 120, width: 120 }"
          image="/static/images/empty.png"
          tip="暂无匹配的专家"
        />
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60rpx;

  .search-box {
    padding: 16rpx 24rpx;
    background: #fff;
    margin-bottom: 16rpx;
  }

  .scroll-container {
    height: calc(100vh - 200rpx);
  }

  .expert-list {
    margin: 16rpx 0;

    .expert-item {
      padding: 24rpx;
      background: #fff;
      border-radius: 16rpx;

      .expert-item-top {
        display: flex;

        .expert-item-avatar {
          width: 112rpx;
          height: 112rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .expert-item-info {
          flex: 1;

          .row {
            .name {
              color: rgba(0, 0, 0, 0.85);
              text-align: center;
              font-family: 'PingFang SC';
              font-size: 40rpx;
              font-style: normal;
              font-weight: 500;
              margin-right: 16rpx;
            }

            .title {
              color: rgba(0, 0, 0, 0.45);
              text-align: center;
              font-family: 'PingFang SC';
              font-size: 28rpx;
              font-style: normal;
              font-weight: 500;
              margin-right: 16rpx;
            }
          }

          .hospital {
            color: rgba(0, 0, 0, 0.85);
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            margin: 16rpx 0;

            text:first-child {
              margin-right: 16rpx;
            }
          }

          .time {
            color: rgba(0, 0, 0, 0.45);
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
          }
        }

        .expert-item-icon {
          width: 40rpx;
          height: 40rpx;
          margin-top: 35rpx;
        }
      }

      .expert-item-stats {
        display: flex;
        justify-content: space-between;
        margin: 24rpx 0;
        padding: 0 24rpx;

        .expert-item-stats-item {
          text-align: center;

          text:first-child {
            color: rgba(0, 0, 0, 0.45);
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            margin-right: 8rpx;
          }

          .expert-item-stats-item-value {
            color: #ff9400;
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 500;
          }
        }
      }

      .expert-item-stats-tabs {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .expert-item-stats-tabs-item {
          border-radius: 40rpx;
          background: #ebf5ff;
          padding: 4rpx 16rpx;
          text-align: center;
          margin-right: 16rpx;
          margin-bottom: 12rpx;

          text {
            color: #007fee;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.empty-tip-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh; // 可根据页面高度调整
}
</style>
