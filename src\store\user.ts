import type { IUserInfoVo } from '@/api/types/login'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getUserInfoAPI,
  smsLoginAPI,
} from '@/api/login'
import { toast } from '@/utils/toast'

// 初始化状态
const userInfoState = {
  username: '', // 用户名
  avatar: '/static/images/default-avatar.png', // 头像
  token: '', // 令牌
  patients: [], // 就诊人集合数组不存在则，跳到添加就诊人界面
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义用户信息
    const userInfo = ref({ ...userInfoState })
    // 设置用户信息
    const setUserInfo = (val) => {
      // console.log('设置用户信息', val)
      // 若头像为空 则使用默认头像
      if (!val.avatar) {
        val.avatar = userInfoState.avatar
      }
      else {
        val.avatar = 'https://oss.laf.run/ukw0y1-site/avatar.jpg?feige'
      }
      userInfo.value = val
    }
    // 设置用户头像
    const setUserAvatar = (avatar: string) => {
      userInfo.value.avatar = avatar
      // console.log('设置用户头像', avatar)
      // console.log('userInfo', userInfo.value)
    }

    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      const res = await getUserInfoAPI()
      const userInfo = res.data as IUserInfoVo
      setUserInfo(userInfo)
      uni.setStorageSync('userInfo', userInfo)
      uni.setStorageSync('token', userInfo.token)
      // TODO 这里可以增加获取用户路由的方法 根据用户的角色动态生成路由
      return res
    }
    /**
     * 用户登录
     */
    const login = async (phonenumber: string, smsCode: string) => {
      // 1. 调用登录接口，获取 token
      const res = await smsLoginAPI({ phonenumber, smsCode })
      // 兼容不同返回结构
      const data = res.data as { token?: string, msg?: string } | undefined
      const token = (data?.token || data?.msg) || (res as any).token || (res as any).msg || ''
      if (!token) {
        toast.error('登录失败，未获取到 token')
        throw new Error('登录失败，未获取到 token')
      }
      // 2. 先存储 token 到本地
      uni.setStorageSync('token', token)
      // 3. 再获取用户信息
      const userRes = await getUserInfoAPI()
      const userInfo = userRes.data as IUserInfoVo
      // 4. 合并 token 到用户信息
      const userInfoWithToken = { ...userInfo, token }
      // 5. 存储到 Pinia
      setUserInfo(userInfoWithToken)
      // 6. 存储到本地缓存
      uni.setStorageSync('userInfo', userInfoWithToken)
      // 7. 提示
      toast.success('登录成功')
      // 8. 跳转到首页或重定向页
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1] as any
      let redirect = '/pages/index/index'
      if (currentPage && currentPage.options && currentPage.options.redirect) {
        redirect = decodeURIComponent(currentPage.options.redirect)
      }
      uni.reLaunch({ url: redirect })

      return res
    }

    /**
     * 微信登录
     */
    const wxLogin = async (token: string) => {
      if (!token) {
        toast.error('微信登录失败，未获取到 token')
        throw new Error('微信登录失败，未获取到 token')
      }
      try {
        // 1. 先存储 token
        uni.setStorageSync('token', token)
        // 2. 再获取用户信息
        const userRes = await getUserInfoAPI()
        const userInfo = userRes.data as IUserInfoVo
        // 3. 合并 token 到用户信息
        const userInfoWithToken = { ...userInfo, token }
        // 4. 存储到 Pinia
        setUserInfo(userInfoWithToken)
        // 5. 存储到本地缓存
        uni.setStorageSync('userInfo', userInfoWithToken)
        // 6. 提示
        toast.success('微信登录成功')
        return userRes
      }
      catch (error) {
        console.error('微信登录获取用户信息失败:', error)
        toast.error('获取用户信息失败')
        throw error
      }
    }
    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...userInfoState }
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    }

    const patientId = ref('') // 就诊人id
    const code = ref('') // 微信登录code
    const wxInfo = ref({
      sessionKey: '',
      openid: '',
      unionid: ''
    }) // 微信信息

    // 设置微信登录code
    const setCode = (val: string) => {
      code.value = val
    }

    // 设置微信信息
    const setWxInfo = (info: { sessionKey: string, openid: string, unionid?: string }) => {
      wxInfo.value = {
        sessionKey: info.sessionKey,
        openid: info.openid,
        unionid: info.unionid || ''
      }
    }

    return {
      userInfo,
      login,
      wxLogin,
      getUserInfo,
      setUserInfo,
      setUserAvatar,
      removeUserInfo,
      patientId,
      code,
      setCode,
      wxInfo,
      setWxInfo,
    }
  },
  {
    persist: true,
  },
)
