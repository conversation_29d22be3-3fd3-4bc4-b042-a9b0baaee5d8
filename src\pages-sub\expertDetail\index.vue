<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useToast } from 'wot-design-uni'
import {
  getExpertDetailAPI,
  getExpertEvaluateAPI,
  getExpertRecordAPI,
} from '@/api/listOfExperts'
import { useLoadingStore } from '@/store'
import { checkUserLogin } from '@/utils'
import FgNavbar from '../../components/fg-navbar-lucency/fg-navbar.vue'

// 页面loading
const loadingStore = useLoadingStore()

const toast = useToast()

const BASE_URL = import.meta.env.VITE_IMG_URL

const id = ref('')
// 专家信息对象
const expert = ref({
  avatar: '', // 头像
  name: '', // 姓名
  title: '', // 职称
  hospital: '', // 医院
  department: '', // 科室
  startConsultationTime: '', // 接诊开始时间
  endConsultationTime: '', // 接诊结束时间
  score: 0, // 评分
  replyNum: 0, // 月回答
  patientNum: 0, // 接诊数
  chronicDiseases: [], // 擅长领域
  introduction: '', // 专家简介
})

// 患者评价
const comments = ref([
  {
    patientAvatar: '', // 头像
    patientName: '', // 患者姓名
    createTime: '', // 创建时间
    score: 0, // 评分
    content: '',
  },
])
const pages = ref({
  pageNum: 1,
  pageSize: 10,
  expertId: '', // 专家id
})
// 总评价数
const totalComments = ref(0)
// 是否显示弹窗
const showDialog = ref(false)

// 用户登录状态
const isLoggedIn = ref(false)

// 专家简介展开逻辑
const showAllIntro = ref(false)
const showExpandIntro = ref(false)

onMounted(() => {
  updateIntro()
})

function updateIntro() {
  const intro = expert.value.introduction || ''
  // 简单判断：如果内容超过一定长度就显示"更多"按钮
  if (intro.length > 50) {
    showExpandIntro.value = true
  }
  else {
    showExpandIntro.value = false
  }
}

// 上传病史
function handleUpload() {
  showDialog.value = false
  // 可在此处添加跳转上传病史页面逻辑
  uni.navigateTo({
    url: '/pages-sub/medical_history/upload',
  })
}
// 继续咨询
async function handleContinue() {
  const res = await getExpertRecordAPI()
  if (typeof res.data === 'string') {
    // 没有病史，弹窗
    showDialog.value = true
  }
  else {
    // 有病史，直接跳转
    showDialog.value = false
    uni.navigateTo({
      url: `/pages-sub/questions/index?id=${id.value}`,
    })
  }
}
// 弹窗内直接跳转咨询
function jumpToConsult() {
  showDialog.value = false
  uni.navigateTo({
    url: `/pages-sub/questions/index?id=${id.value}`,
  })
}
// 展开
function handleExpandIntro() {
  uni.navigateTo({
    url: `/pages-sub/expertDetail/synopsis?introduction=${expert.value.introduction}&id=${id.value}`,
  })
}
// 更多评价
function handleMore() {
  uni.navigateTo({
    url: `/pages-sub/expertDetail/comments?id=${id.value}`,
  })
}
// 患者是否填写过记录
async function getExpertRecord() {
  const res = await getExpertRecordAPI()
  console.log(res.data, '填写记录')
  showDialog.value = res.data != null
}
// 获取专家详情
async function getExpertDetail() {
  const res = await getExpertDetailAPI(id.value)
  expert.value = res.data as any
  updateIntro() // 数据更新后重新处理简介
}
// 获取患者评价列表
async function getExpertEvaluate() {
  const res = await getExpertEvaluateAPI({
    ...pages.value,
    expertId: id.value,
  })
  comments.value = (res as any).rows || []
  totalComments.value = (res as any).total
}

// 隐藏姓名
function maskName(name: string) {
  if (!name)
    return ''
  return name[0] + '*'.repeat(name.length - 1)
}

// 检查登录状态
function checkLoginStatus() {
  isLoggedIn.value = checkUserLogin()
}

onLoad((options) => {
  id.value = options.id
  // 检查登录状态
  checkLoginStatus()
  // getExpertRecord() 暂时不调用
  loadingStore.withLoading(async () => {
    await getExpertDetail()
    await getExpertEvaluate()
  })
})
onShow(() => {
  // 每次显示页面时都检查登录状态
  checkLoginStatus()
  loadingStore.withLoading(async () => {
    await getExpertDetail()
    await getExpertEvaluate()
  })
})
</script>

<template>
  <view
    class="page"
    style="
      background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/expertDetail.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    "
  >
    <!-- 自定义透明导航栏 -->
    <FgNavbar :left-arrow="true" left-text="">
      专家主页
    </FgNavbar>
    <view class="expert-card">
      <image :src="BASE_URL + expert.avatar" class="avatar" />
      <view class="info">
        <view class="row name-title">
          <text class="name-highlight">
            {{ expert.name }}
          </text>
          <text class="title">
            {{ expert.title }}
          </text>
        </view>
        <view class="row">
          <text class="hospital">
            {{ expert.hospital }}
          </text>
          <text class="specialty">
            {{ expert.department }}
          </text>
        </view>
        <view class="row">
          <text class="time">
            接诊时间:{{ expert.startConsultationTime }} ~
            {{ expert.endConsultationTime }}
          </text>
        </view>
      </view>
    </view>
    <!-- 好评 -->
    <view class="good-comment">
      <view class="good-comment-title">
        <text class="stat-label">
          好评
        </text>
        <text class="stat-value">
          {{ expert.score }}
        </text>
      </view>
      <view class="good-comment-title">
        <text class="stat-label">
          月回答
        </text>
        <text class="stat-value">
          {{ expert.replyNum }}
        </text>
      </view>
      <view class="good-comment-title">
        <text class="stat-label">
          接诊数
        </text>
        <text class="stat-value">
          {{ expert.patientNum }}
        </text>
      </view>
    </view>
    <!-- 擅长 -->
    <view class="expert-specialty">
      <view class="expert-specialty-title">
        擅长领域
      </view>
      <view class="specialty-tags">
        <text
          v-for="(item, idx) in expert.chronicDiseases"
          :key="idx"
          class="specialty-tag"
        >
          {{ item.diseaseName }}
        </text>
      </view>
    </view>
    <!-- 专家简介 -->
    <view class="expert-intro">
      <view class="expert-intro-header">
        <view class="expert-intro-title">
          专家简介
        </view>
        <view
          v-if="showExpandIntro && !showAllIntro"
          class="expand-btn-header"
          @click.stop="handleExpandIntro"
        >
          查看更多
          <image
            src="/static/svg/arrows.svg"
            mode="scaleToFill"
            class="arrow"
          />
        </view>
      </view>
      <view class="expert-intro-content">
        <text v-if="!showAllIntro" class="intro-text">
          {{ expert.introduction }}
        </text>
        <text v-else>
          {{ expert.introduction }}
        </text>
      </view>
    </view>
    <!-- 患者评价 -->
    <view class="expert-comments">
      <view class="comments-header">
        <view>
          患者评价
          <text class="comments-count">
            ({{ totalComments }})
          </text>
        </view>
        <view class="comments-more" @click="handleMore">
          更多评价
          <image
            src="/static/svg/arrows.svg"
            mode="scaleToFill"
            class="arrow"
          />
        </view>
      </view>
      <view class="comments-list">
        <template v-if="comments.length === 0">
          <view class="empty-comments">
            <image
              src="/static/images/empty.png"
              class="empty-img"
            />
            <view class="empty-tip">
              暂无评价
            </view>
          </view>
        </template>
        <template v-else>
          <view v-for="(item, idx) in comments" :key="idx" class="comment-item">
            <image
              :src="item.patientAvatar ? BASE_URL + item.patientAvatar : '../../static/images/avatar.jpg'"
              mode="aspectFill"
              class="comment-avatar"
            />
            <view class="comment-main">
              <view class="comment-header-row">
                <text class="comment-name">
                  {{ maskName(item.patientName) }}
                </text>
                <text class="comment-date">
                  {{ item.createTime }}
                </text>
              </view>
              <view class="comment-stars-row">
                <image
                  v-for="i in 5"
                  :key="i"
                  :src="
                    i <= item.score
                      ? '/static/svg/star-active.svg'
                      : '/static/svg/star-inactive.svg'
                  "
                  mode="scaleToFill"
                  class="star-img"
                />
              </view>
              <view class="comment-content no-indent">
                {{ item.content }}
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
    <view v-if="isLoggedIn" class="fixed-bottom-btn">
      <view class="btn" @click="handleContinue">
        立即咨询
      </view>
    </view>
    <view v-if="showDialog" class="dialog-mask">
      <view class="dialog-content">
        <view class="dialog-title">
          未检测到病史信息
        </view>
        <view class="dialog-desc">
          病史信息能够帮助医生了解患者的基本情况，对于判断疾病的可能性和方向具有关键作用。‌
        </view>
        <view class="dialog-btns">
          <button class="dialog-btn" @click="handleUpload">
            上传病史
          </button>
          <button class="dialog-btn primary" @click="jumpToConsult">
            继续咨询
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  padding-bottom: 230rpx;
  .expert-card {
    display: flex;
    align-items: flex-start;
    border-radius: 16rpx;
    padding: 32rpx;
    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      margin-right: 32rpx;
      object-fit: cover;
      background: #f5f5f5;
    }
    .info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      .row {
        display: flex;
        align-items: center;
        // margin: 12rpx 0;

        .name-highlight {
          color: #333;
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 40rpx;
          font-style: normal;
          font-weight: 500;
          margin-right: 16rpx;
        }
        .title {
          color: #333;
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 28rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 32rpx; /* 114.286% */
        }
        .hospital,
        .specialty {
          font-size: 28rpx;
          color: #444;
          margin-right: 16rpx;
        }
        .time {
          font-size: 26rpx;
          color: #888;
          margin-top: 24rpx;
        }
      }
      .name-title {
        margin-bottom: 8rpx;
      }
    }
  }
  .good-comment {
    margin: 0 32rpx;
    border-radius: 8px 8px 0px 0px;
    background: #007fee;
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    .good-comment-title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 16rpx 24rpx 32rpx 24rpx;
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        margin-right: 16rpx;
      }
      .stat-value {
        color: #fff;
        font-family: DIN;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 16px; /* 100% */
      }
    }
  }
  .expert-specialty {
    margin: -24rpx 32rpx 0 32rpx;
    position: relative;
    z-index: 2;
    border-radius: 16rpx;
    background: #fff;
    padding: 24rpx;
    .expert-specialty-title {
      color: #333;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 32rpx; /* 100% */
      margin-bottom: 28rpx;
    }
    .specialty-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx 24rpx;
    }

    .specialty-tag {
      background: #ebf5ff;
      border-radius: 40rpx;
      padding: 4rpx 16rpx;
      font-size: 24rpx;
      display: inline-block;
      color: #4d80f0;
      text-align: justify;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
  .expert-intro {
    margin: 0 32rpx;
    background-color: #fff;
    margin-top: 16rpx;
    border-radius: 16rpx;
    padding: 24rpx;

    .expert-intro-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
    }

    .expert-intro-title {
      color: #333;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 32rpx; /* 100% */
    }

    .expand-btn-header {
      font-size: 24rpx;
      color: #999;
      font-family: 'PingFang SC';
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      .arrow {
        width: 25rpx;
        height: 25rpx;
      }
    }

    .expert-intro-content {
      color: #333;
      font-family: 'PingFang SC';
      font-size: 26rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 40rpx;
      word-break: break-all;
    }

    .intro-text {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制为2行 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .expert-comments {
    margin: 0 32rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-top: 16rpx;
    .comments-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32rpx;
      font-weight: 700;
      color: #333;
      margin-bottom: 24rpx;
      .comments-count {
        font-size: 24rpx;
        font-weight: 400;
        color: #bdbdbd;
        margin-left: 8rpx;
      }
      .comments-more {
        font-size: 24rpx;
        color: #999;
        font-family: 'PingFang SC';
        font-weight: 400;
        display: flex;
        align-items: center;
        .arrow {
          width: 25rpx;
          height: 25rpx;
          margin-top: 3rpx;
        }
      }
    }
    .comments-list {
      .comment-item {
        display: flex;
        padding: 24rpx 0;
        border-bottom: 1px solid #f0f0f0;
        &:last-child {
          border-bottom: none;
        }
        .comment-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 16rpx;
          object-fit: cover;
        }
        .comment-main {
          flex: 1;
          .comment-header-row {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;
            .comment-name {
              font-size: 28rpx;
              color: #333;
              font-weight: 700;
              margin-right: 8rpx;
            }
            .comment-date {
              font-size: 24rpx;
              color: #bdbdbd;
              font-family: 'PingFang SC';
              font-style: normal;
              margin-left: auto;
            }
          }
          .comment-stars-row {
            margin-bottom: 8rpx;
            .star-img {
              width: 28rpx;
              height: 26rpx;
              display: inline-block;
              margin-right: 4rpx;
              vertical-align: middle;
            }
          }
          .comment-content {
            font-size: 28rpx;
            color: #333;
            line-height: 40rpx;
            margin-top: 4rpx;
          }
        }
      }
    }
  }
  .fixed-bottom-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24rpx 0 48rpx 0;
    background: #fff;
    border-bottom-left-radius: 32rpx;
    border-bottom-right-radius: 32rpx;
    box-shadow: 0 -8rpx 24rpx 0 rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;
    z-index: 10;

    .btn {
      width: 90vw;
      height: 88rpx;
      background: #1890ff;
      color: #fff;
      border-radius: 44rpx;
      font-size: 32rpx;
      text-align: center;
      line-height: 88rpx;
      border: none;
    }
  }
}
.comment-content.no-indent {
  margin-left: -96rpx;
}
.dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-content {
  background: #fff;
  border-radius: 8rpx;
  padding: 64rpx 56rpx 48rpx 56rpx;
  width: 558rpx;
  box-sizing: border-box;
  text-align: center;
}
.dialog-title {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 32rpx;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  font-family: 'PingFang SC';
  line-height: 18px; /* 100% */
}
.dialog-desc {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 64rpx;
  font-family: 'PingFang SC';
}
.dialog-btns {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}
.dialog-btn {
  flex: 1;
  border-radius: 100px;
  font-size: 28rpx;
  background: #f5f5f5;
  color: #333;
  width: 184rpx;
  height: 72rpx;
  justify-content: center;
  align-items: center;
  line-height: 72rpx;
  border: 1px solid #bfbfbf;
}
.dialog-btn.primary {
  background: #007fee;
  color: #fff;
}
.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  .empty-img {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 16rpx;
  }
  .empty-tip {
    color: #bdbdbd;
    font-size: 28rpx;
  }
}
</style>
