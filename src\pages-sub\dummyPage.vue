<route lang="json5" type="page">
    {
      style: {
        navigationBarTitleText: '',
      },
    }
  </route>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

const listData = [
  {
    title: '特色乡村',
    text: '在城市的喧嚣之外，隐匿着无数充满魅力的特色乡村，它们宛如一颗颗璀璨的明珠，散发着独特的光芒，成为人们心灵的栖息之所。特色乡村，首先美在自然风光。这里有广袤无垠的田野，四季变换着色彩。春天，嫩绿的麦苗在微风中轻轻摇曳，仿佛一片绿色的海洋泛起层层涟漪；夏天，金黄的油菜花肆意绽放，那浓郁的花香弥漫在空气中，让人心旷神怡；秋天，沉甸甸的稻穗压弯了枝头，农民们在田间忙碌，丰收的喜悦洋溢在每一个人的脸上；冬天，皑皑白雪覆盖着大地，整个乡村银装素裹，宛如童话世界。还有那清澈见底的溪流，溪水潺潺流淌，鱼儿在水中自由自在地游弋，溪边的垂柳依依，为乡村增添了几分灵动之美。除了自然风光，特色乡村还承载着深厚的民俗文化。古老的建筑错落有致，每一块砖石、每一片瓦片都诉说着岁月的故事。传统的手工艺代代相传，如精美的剪纸、细腻的刺绣、古朴的木雕等，无不展现着村民们的智慧和创造力。丰富多彩的民俗活动更是乡村生活的一大亮点，热闹非凡的庙会、喜庆的社火表演、悠扬的山歌对唱，让人们感受到乡村文化的独特魅力。在这里，邻里之间互帮互助，淳朴的民风让人与人之间的关系更加温暖。特色乡村的发展，也离不开特色产业的支撑。有的乡村凭借丰富的自然资源，发展生态农业，种植绿色有机蔬菜、水果，打造生态农产品品牌；有的乡村利用独特的自然风光，开发乡村旅游，建设农家乐、民宿，吸引了众多游客前来观光度假；还有的乡村结合传统手工艺，发展文化创意产业，将传统手工艺品转化为具有市场竞争力的文化产品。这些特色产业不仅为村民们带来了可观的收入，也推动了乡村经济的繁荣发展。特色乡村，是自然与人文的完美融合，是传统与现代的和谐共生。它既保留了乡村的原汁原味，又注入了新的活力与生机。让我们走进特色乡村，感受它的独特魅力，共同守护这片美丽的家园。',
  },
  {
    title: '名宿乡村',
    text: '在快节奏生活的当下，人们越发渴望逃离城市的喧嚣，寻找一处宁静的世外桃源。民宿乡村，正是这样一个充满魅力的地方，它将乡村的自然美景与民宿的温馨舒适完美融合，为人们提供了一个独特的休闲度假体验。走进民宿乡村，映入眼帘的是一幅幅如诗如画的田园风光。连绵起伏的山峦，郁郁葱葱的树木，一望无际的田野，构成了乡村最天然的底色。这里的空气清新宜人，每一口呼吸都仿佛带着泥土的芬芳和花草的清香。清晨，阳光透过薄雾洒在村庄，唤醒了沉睡的大地；傍晚，夕阳的余晖将整个乡村染成一片金黄，美不胜收。民宿作为乡村的特色亮点，各具风格。有的民宿保留了传统的乡村建筑风格，木质的门窗、古朴的砖瓦，让人仿佛穿越回了过去；有的民宿则融入了现代的设计元素，简约而不失时尚，为游客带来舒适的居住体验。民宿内部的装饰也充满了乡村特色，墙上挂着的农民画、摆放着的手工艺品，无不展示着乡村的文化底蕴。在这里，游客可以远离城市的繁华，享受一段宁静、惬意的时光。民宿乡村不仅为游客提供了一个休息的地方，还为他们带来了丰富的乡村体验。游客可以参与农事活动，亲自下地采摘新鲜的蔬菜、水果，感受劳动的乐趣；可以跟着当地村民学习传统的手工艺，如编织、陶艺等，领略乡村文化的魅力；还可以品尝到地道的农家美食，如柴火炖鸡、手工面条等，让味蕾沉浸在乡村的美味之中。对于乡村来说，民宿的发展也带来了新的机遇。它吸引了大量的游客，为乡村带来了人气和收入。同时，民宿的发展也带动了周边产业的发展，如农产品销售、餐饮服务等，促进了乡村经济的繁荣。此外，为了满足游客的需求，乡村的基础设施也得到了不断完善，道路更加宽敞平坦，水电供应更加稳定，网络覆盖更加全面。民宿乡村，是城市与乡村的桥梁，是传统与现代的结合。它以独特的魅力吸引着人们前来，让人们在忙碌的生活中找到一片宁静的港湾，感受乡村的美好，体验不一样的生活方式。让我们走进民宿乡村，开启一场与自然、与乡村的美好邂逅。',
  },
  {
    title: '旅游乡村',
    text: '在繁华都市的车水马龙之外，旅游乡村正以其独有的魅力，成为人们心灵的归处与向往的度假胜地。它承载着自然的馈赠、人文的底蕴，为游客们开启一场远离尘嚣、回归本真的奇妙之旅。踏入旅游乡村，便如同踏入了一个被岁月温柔以待的世界。这里的自然风光别具一格，蜿蜒的溪流沿着山谷潺潺流淌，清澈见底的水波下，五彩斑斓的石子清晰可见。溪边垂柳依依，细长的柳枝随风轻舞，似是在向远道而来的游客招手。远处，错落有致的梯田如大地的指纹，随着季节的更迭，变幻出不同的色彩。春季，嫩绿的秧苗在微风中轻轻摇曳；秋季，金黄的稻穗沉甸甸地低垂着，宣告着丰收的喜悦。丰富多样的乡村活动，是旅游乡村吸引游客的重要因素。农事体验让游客亲身体验乡村生活的质朴，挽起裤脚踏入稻田，参与插秧、收割，感受粮食的来之不易；亲手采摘新鲜的水果、蔬菜，品尝大自然最本真的味道，乐趣无穷。民俗文化活动更是充满魅力，热闹非凡的舞龙舞狮表演，激昂的锣鼓声中，表演者们身手矫健，彩龙腾飞、瑞狮欢舞，尽显乡村的活力与热情；古朴的乡村戏曲，咿呀婉转的唱腔，演绎着代代相传的故事，让游客沉浸在浓郁的文化氛围中。旅游乡村的发展，为当地带来了显著的变化。经济上，大量游客的涌入，带动了餐饮、住宿、农产品销售等行业的蓬勃发展。特色民宿如雨后春笋般涌现，以独特的乡村风格和贴心的服务，为游客提供舒适的居住环境；农家菜馆里，地道的乡村美食，让游客大饱口福，也为村民增加了收入来源。文化方面，旅游的发展让乡村的传统文化得以传承和弘扬，古老的手工艺、民俗节庆重新焕发生机，吸引着更多人关注乡村文化。旅游乡村，是自然与人文交织的画卷，是传统与现代碰撞的舞台。它为游客提供了一个远离城市喧嚣、回归自然怀抱的机会，也为乡村的发展注入了新的活力。让我们走进旅游乡村，探寻乡野间的诗与远方，感受那份独特的美好与宁静。',
  },
]

// 定义接口类型
interface CheckInItem {
  id: number
  name: string
  image: string
  description: string
  tags: string[]
  hotLevel: string
  checkInCount: string
  isCheckedIn: boolean
  checkInTime: string
}

interface ActivityItem {
  title: string
  desc: string
  time: string
  location: string
  image: string
  status: string
  statusText: string
}

interface KingItem {
  title: string
  date: string
}

// 响应式数据
const title = ref('')
const kingList = ref<KingItem[]>([
  {
    title: '养老院打扫卫生',
    date: '2024-08-01',
  },
  {
    title: '日常清洁河道垃圾',
    date: '2024-09-12',
  },
  {
    title: '乡村电影放映',
    date: '2024-12-30',
  },
  {
    title: '乡村公共基础设施检修',
    date: '2025-01-05',
  },
])

const activityList = ref<ActivityItem[]>([
  {
    title: '乡村环保志愿活动',
    desc: '参与河道清洁，保护乡村生态环境，共建美丽家园',
    time: '2024-12-25 09:00',
    location: '福安市溪潭镇',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img1.jpeg',
    status: 'recruiting',
    statusText: '招募中',
  },
  {
    title: '传统文化体验活动',
    desc: '学习传统手工艺，体验乡村文化魅力',
    time: '2024-12-28 14:00',
    location: '南靖县官洋村',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img2.jpeg',
    status: 'recruiting',
    statusText: '招募中',
  },
  {
    title: '乡村摄影大赛',
    desc: '用镜头记录乡村美景，展现乡村振兴成果',
    time: '2025-01-05 10:00',
    location: '元阳县阿者科村',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img3.jpeg',
    status: 'upcoming',
    statusText: '即将开始',
  },
])

const checkInList = ref<CheckInItem[]>([
  {
    id: 1,
    name: '云南元阳梯田',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img1.jpeg',
    description: '世界文化遗产，日出云海绝美景色',
    tags: ['日出', '云海', '梯田'],
    hotLevel: '🔥🔥🔥🔥🔥',
    checkInCount: '12.8万',
    isCheckedIn: false,
    checkInTime: '',
  },
  {
    id: 2,
    name: '福建土楼群',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img2.jpeg',
    description: '客家围屋建筑奇观，古朴神秘',
    tags: ['古建筑', '客家文化', '摄影'],
    hotLevel: '🔥🔥🔥🔥',
    checkInCount: '8.6万',
    isCheckedIn: true,
    checkInTime: '2024-12-20 14:30',
  },
  {
    id: 3,
    name: '湖南十八洞村',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img3.jpeg',
    description: '精准扶贫第一村，苗寨风情浓郁',
    tags: ['苗族', '扶贫', '民俗'],
    hotLevel: '🔥🔥🔥',
    checkInCount: '5.2万',
    isCheckedIn: false,
    checkInTime: '',
  },
  {
    id: 4,
    name: '安徽宏村',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img1.jpeg',
    description: '徽派建筑典范，水墨画般的古村落',
    tags: ['徽派', '古村', '水乡'],
    hotLevel: '🔥🔥🔥🔥🔥',
    checkInCount: '15.3万',
    isCheckedIn: false,
    checkInTime: '',
  },
  {
    id: 5,
    name: '浙江安吉竹海',
    image: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img2.jpeg',
    description: '绿色海洋，天然氧吧',
    tags: ['竹海', '绿色', '清新'],
    hotLevel: '🔥🔥🔥🔥',
    checkInCount: '9.7万',
    isCheckedIn: false,
    checkInTime: '',
  },
])

const myCheckInCount = ref(1)
const totalHotSpots = ref(50)

// 计算属性
const text = computed(() => {
  return listData.find(item => item.title === title.value)?.text || '暂无数据'
})

// 方法函数
function getCurrentTime() {
  const now = new Date()
  return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
}

function checkIn(item: CheckInItem) {
  if (item.isCheckedIn) {
    uni.showToast({
      title: '已经打过卡啦~',
      icon: 'none',
    })
    return
  }

  item.isCheckedIn = true
  item.checkInTime = getCurrentTime()
  myCheckInCount.value += 1

  // 模拟增加打卡人数
  const count = Number.parseFloat(item.checkInCount.replace('万', ''))
  item.checkInCount = `${(count + 0.1).toFixed(1)}万`

  uni.showToast({
    title: '打卡成功！成为网红啦~',
    icon: 'success',
  })
}

function shareLocation(item: CheckInItem) {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      uni.showToast({
        title: '分享成功',
        icon: 'success',
      })
    },
  })
}

// 生命周期钩子
onLoad((options: any) => {
  if (options?.text) {
    title.value = options.text
    uni.setNavigationBarTitle({
      title: options.text,
    })
  }
})
</script>

<template>
  <view class="page-details">
    <template v-if="title === '乡村推荐'">
      <view class="box">
        <view class="title">
          福建地区
        </view>
        <view class="content">
          <text class="text">
            宁德福安市溪潭镇廉村：
          </text> 是古朴雅致的明清古村落，也是唐朝福建第一个进士薛令之的故乡，被喻为 “开闽进士第一村”。这里有长 1200 米的古城墙，26
          座明、清时期的古民居，还有祭祀薛令之的
          “后湖宫”
          以及陈氏宗祠等建筑，其耕读传家的精神传承至今。
        </view>
        <view class="content">
          <text class="text">
            漳州南靖县官洋村：
          </text>
          是土楼文化、闽南文化、客家文化的 “大观园”。通过改善和活化利用土楼，融入休闲业态，展现出独特的乡村魅力，游客可以在这里欣赏到精美的土楼建筑，感受不同文化的交融。
        </view>
      </view>
      <view class="box">
        <view class="title">
          云南地区
        </view>
        <view class="content">
          <text class="text">
            红河哈尼族彝族自治州元阳县阿者科村：
          </text>
          位于世界文化遗产哈尼梯田核心区，当地村民以梯田、房屋、生产生活方式等资源入股，走出了一条全民共建共享的旅游发展之路，游客可以在这里领略到壮观的哈尼梯田风光，体验独特的哈尼族文化。
        </view>
        <view class="content">
          <text class="text">
            腾冲市银杏村：
          </text>
          以两千多亩连片栽种的银杏树而闻名，是远近闻名的 “金秋网红”。秋天时，银杏村如童话世界般唯美浪漫，连绵的山林绽放着金光，屋舍散落在其中，房前屋后、道路小巷铺满银杏落叶，美不胜收。
        </view>
      </view>
      <view class="box">
        <view class="title">
          湖南地区
        </view>
        <view class="content">
          <text class="text">
            湘西土家族苗族自治州花垣县十八洞村：
          </text> 是 “精准扶贫”
          理念走向全中国的起点。如今通过文旅融合激发乡村发展新动能，向世界展示了中国乡村振兴新图景，游客可以在这里了解到精准扶贫的成果，感受苗族等少数民族的文化风情。
        </view>
        <view class="content">
          <text class="text">
            张家界市武陵源区中湖乡野溪铺村：
          </text>
          地处世界自然遗产地张家界武陵源核心景区西大门，这里有独特的石英砂岩峰林地貌，村庄周边群山环绕，溪流潺潺，空气清新，是休闲度假和户外运动的好去处。
        </view>
      </view>
      <view class="box">
        <view class="title">
          安徽地区
        </view>
        <view class="content">
          <text class="text">
            滁州市凤阳县小岗村：
          </text> 是中国农村改革的主要发源地。在乡村旅游发展过程中，小岗村大力发掘在地资源，向国内外游客述说中国农村改革的历程与改革开放 40
          余年来中国社会发生的巨变，游客可以参观 “大包干” 纪念馆，感受中国农村改革的历史脉搏。
        </view>
        <view class="content">
          <text class="text">
            宣城市绩溪县家朋乡：
          </text>
          有 “摄影小镇” 之称，春天油菜花开时，金色海洋层层叠叠，美得让人陶醉，这里还有众多徽派建筑，是体验徽派文化和欣赏田园风光的好去处。
        </view>
      </view>
      <view class="box">
        <view class="title">
          浙江地区
        </view>
        <view class="content">
          <text class="text">
            丽水市龙泉市溪头村：
          </text>
          是龙泉青瓷烧制技艺的传承地，当地依托青瓷文化，打造了瓷源文化传承空间、现代艺术创作基地、瓷艺特色文化节庆等旅游品牌，游客可以在这里参观青瓷制作工坊，体验青瓷制作工艺，感受青瓷文化的魅力。
        </view>
        <view class="content">
          <text class="text">
            湖州市安吉县：
          </text>
          是美丽乡村的发源地，登上过 “最美中国榜”，也是 “都市后花园”。这里有漫山遍野的竹海，还有茶园、花海、峡谷、飞瀑等美景，一切关于美丽乡村的想象，都能在这里找到，是休闲度假、亲近自然的绝佳之地。
        </view>
      </view>
    </template>
    <template v-if="title === '全部乡村'">
      <view class="box">
        <view class="title">
          云南阿者科村
        </view>
        <view class="content">
          当地村民以梯田、房屋、生产生活方式等资源入股，走出了一条全民共建共享的旅游发展之路。在这里可以欣赏到壮丽的哈尼梯田，层层叠叠的梯田从山顶延伸到山脚，四季景色各异，春季灌满水如镜面般反射天光，秋季稻谷成熟一片金黄，美不胜收。
        </view>
      </view>
      <view class="box">
        <view class="title">
          福建官洋村
        </view>
        <view class="content">
          是土楼文化、闽南文化、客家文化的
          “大观园”。土楼建筑独具特色，如圆形的怀远楼、方形的和贵楼等，建筑工艺精湛，内部结构精巧，体现了古人的智慧和创造力。通过改善和活化利用土楼，融入休闲业态，游客可以在这里体验土楼生活，感受不同文化的交融。
        </view>
      </view>
      <view class="box">
        <view class="title">
          湖南十八洞村
        </view>
        <view class="content">
          “精准扶贫”
          理念在这里走向全中国，如今通过文旅融合激发乡村发展新动能。村里保留了许多土家族、苗族的传统建筑和民俗文化，游客可以参观精准扶贫展示馆，了解十八洞村的脱贫历程，还可以品尝当地的特色美食，如苗家酸汤鱼、腊肉等，感受少数民族的热情好客。
        </view>
      </view>
      <view class="box">
        <view class="title">
          四川桃坪村
        </view>
        <view class="content">
          是国家级羌族文化生态保护区核心区、茶马古道的重要节点。桃坪羌寨是其主要景点，寨内的建筑布局巧妙，巷道纵横交错，宛如迷宫，碉楼林立，具有很强的防御性。这里的羌族文化底蕴深厚，游客可以欣赏到精美的羌绣，观看羌族歌舞表演，参与羌历年等传统节日活动。
        </view>
      </view>
      <view class="box">
        <view class="title">
          安徽小岗村
        </view>
        <view class="content">
          是中国农村改革的主要发源地。在这里可以参观大包干纪念馆，了解小岗村在改革开放初期的历史背景和改革历程，感受 “敢为天下先” 的小岗精神。此外，小岗村还发展了现代农业观光园，游客可以体验采摘、农事活动等。
        </view>
      </view>
      <view class="box">
        <view class="title">
          浙江溪头村
        </view>
        <view class="content">
          是龙泉青瓷烧制技艺的传承地。当地依托青瓷文化，打造了瓷源文化传承空间、现代艺术创作基地、瓷艺特色文化节庆等旅游品牌。游客可以参观青瓷博物馆，了解青瓷的历史和制作工艺，还可以亲自参与青瓷制作体验，感受指尖上的艺术。
        </view>
      </view>
      <view class="box">
        <view class="title">
          山东烟墩角村
        </view>
        <view class="content">
          是渔家秧歌、海草房营造技艺等传统文化活态传承之地。这里的海草房是一大特色，用海草苫盖屋顶，冬暖夏凉，造型古朴美观。每年冬季，还会有大量的天鹅从西伯利亚飞来越冬，游客可以在海边观赏到天鹅嬉戏的美景，与大自然亲密接触。
        </view>
      </view>
    </template>
    <template v-if="['特色乡村', '名宿乡村', '旅游乡村'].includes(title)">
      <view class="imgbox">
        <image
          v-if="title === '特色乡村'" class="img"
          src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img1.jpeg"
          mode="scaleToFill"
        />
        <image
          v-if="title === '名宿乡村'" class="img"
          src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img2.jpeg"
          mode="scaleToFill"
        />
        <image
          v-if="title === '旅游乡村'" class="img"
          src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img3.jpeg"
          mode="scaleToFill"
        />
        <view v-if="title === '特色乡村'" class="box">
          <view class="title">
            云南阿者科村
          </view>
          <view class="content">
            当地村民以梯田、房屋、生产生活方式等资源入股，走出了一条全民共建共享的旅游发展之路。在这里可以欣赏到壮丽的哈尼梯田，层层叠叠的梯田从山顶延伸到山脚，四季景色各异，春季灌满水如镜面般反射天光，秋季稻谷成熟一片金黄，美不胜收。
          </view>
        </view>
        <view v-if="title === '特色乡村'" class="box">
          <view class="title">
            福建官洋村
          </view>
          <view class="content">
            是土楼文化、闽南文化、客家文化的
            “大观园”。土楼建筑独具特色，如圆形的怀远楼、方形的和贵楼等，建筑工艺精湛，内部结构精巧，体现了古人的智慧和创造力。通过改善和活化利用土楼，融入休闲业态，游客可以在这里体验土楼生活，感受不同文化的交融。
          </view>
        </view>
        <view v-if="title === '名宿乡村'" class="box">
          <view class="title">
            湖南十八洞村
          </view>
          <view class="content">
            “精准扶贫”
            理念在这里走向全中国，如今通过文旅融合激发乡村发展新动能。村里保留了许多土家族、苗族的传统建筑和民俗文化，游客可以参观精准扶贫展示馆，了解十八洞村的脱贫历程，还可以品尝当地的特色美食，如苗家酸汤鱼、腊肉等，感受少数民族的热情好客。
          </view>
        </view>
        <view v-if="title === '名宿乡村'" class="box">
          <view class="title">
            四川桃坪村
          </view>
          <view class="content">
            是国家级羌族文化生态保护区核心区、茶马古道的重要节点。桃坪羌寨是其主要景点，寨内的建筑布局巧妙，巷道纵横交错，宛如迷宫，碉楼林立，具有很强的防御性。这里的羌族文化底蕴深厚，游客可以欣赏到精美的羌绣，观看羌族歌舞表演，参与羌历年等传统节日活动。
          </view>
        </view>
        <view v-if="title === '旅游乡村'" class="box">
          <view class="title">
            安徽小岗村
          </view>
          <view class="content">
            是中国农村改革的主要发源地。在这里可以参观大包干纪念馆，了解小岗村在改革开放初期的历史背景和改革历程，感受 “敢为天下先” 的小岗精神。此外，小岗村还发展了现代农业观光园，游客可以体验采摘、农事活动等。
          </view>
        </view>
        <view v-if="title === '旅游乡村'" class="box">
          <view class="title">
            浙江溪头村
          </view>
          <view class="content">
            是龙泉青瓷烧制技艺的传承地。当地依托青瓷文化，打造了瓷源文化传承空间、现代艺术创作基地、瓷艺特色文化节庆等旅游品牌。游客可以参观青瓷博物馆，了解青瓷的历史和制作工艺，还可以亲自参与青瓷制作体验，感受指尖上的艺术。
          </view>
        </view>
        <view v-if="title === '旅游乡村'" class="box">
          <view class="title">
            山东烟墩角村
          </view>
          <view class="content">
            是渔家秧歌、海草房营造技艺等传统文化活态传承之地。这里的海草房是一大特色，用海草苫盖屋顶，冬暖夏凉，造型古朴美观。每年冬季，还会有大量的天鹅从西伯利亚飞来越冬，游客可以在海边观赏到天鹅嬉戏的美景，与大自然亲密接触。
          </view>
        </view>
      </view>
    </template>
    <template v-if="title === '公益事业'">
      <view class="undertakings">
        <view v-for="item in kingList" :key="item.date" class="item">
          <text>{{ item.title }}</text>
          <text>{{ item.date }}</text>
        </view>
      </view>
    </template>
    <template v-if="title === '活动招募'">
      <view class="activity-list">
        <view v-for="(activity, index) in activityList" :key="index" class="activity-item">
          <image :src="activity.image" class="activity-image" mode="aspectFill" />
          <view class="activity-info">
            <view class="activity-title">
              {{ activity.title }}
            </view>
            <view class="activity-desc">
              {{ activity.desc }}
            </view>
            <view class="activity-meta">
              <text class="activity-time">
                {{ activity.time }}
              </text>
              <text class="activity-location">
                {{ activity.location }}
              </text>
            </view>
            <view class="activity-status" :class="activity.status">
              {{ activity.statusText }}
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-if="title === '打卡'">
      <view class="hotspot-page">
        <!-- 头部统计 -->
        <view class="stats-header">
          <view class="stats-card">
            <view class="stats-item">
              <text class="stats-number">
                {{ myCheckInCount }}
              </text>
              <text class="stats-label">
                我的打卡
              </text>
            </view>
            <view class="stats-divider" />
            <view class="stats-item">
              <text class="stats-number">
                {{ totalHotSpots }}
              </text>
              <text class="stats-label">
                网红地点
              </text>
            </view>
          </view>
          <view class="header-title">
            <text class="title-main">
              🔥 乡村网红打卡地
            </text>
            <text class="title-sub">
              发现最美乡村，记录美好时光
            </text>
          </view>
        </view>

        <!-- 网红地点列表 -->
        <view class="hotspot-list">
          <view v-for="item in checkInList" :key="item.id" class="hotspot-item">
            <view class="hotspot-image-container">
              <image :src="item.image" class="hotspot-image" mode="aspectFill" />
              <view class="hot-badge">
                {{ item.hotLevel }}
              </view>
              <view v-if="item.isCheckedIn" class="checked-overlay">
                <text class="checked-text">
                  ✓ 已打卡
                </text>
              </view>
            </view>

            <view class="hotspot-content">
              <view class="hotspot-header">
                <text class="hotspot-name">
                  {{ item.name }}
                </text>
                <text class="checkin-count">
                  {{ item.checkInCount }}人打卡
                </text>
              </view>

              <text class="hotspot-desc">
                {{ item.description }}
              </text>

              <view class="hotspot-tags">
                <text v-for="tag in item.tags" :key="tag" class="tag">
                  # {{ tag }}
                </text>
              </view>

              <view v-if="item.isCheckedIn" class="checkin-time">
                打卡时间：{{ item.checkInTime }}
              </view>

              <view class="hotspot-actions">
                <button v-if="!item.isCheckedIn" class="checkin-btn primary" @click="checkIn(item)">
                  📍 立即打卡
                </button>
                <button v-else class="checkin-btn checked" @click="checkIn(item)">
                  ✓ 已打卡
                </button>
                <button class="share-btn" @click="shareLocation(item)">
                  📤 分享
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 打卡攻略 -->
        <view class="tips-section">
          <view class="tips-title">
            📸 打卡攻略
          </view>
          <view class="tips-content">
            <view class="tip-item">
              <text class="tip-icon">
                🌅
              </text>
              <text class="tip-text">
                最佳拍照时间：日出日落时分，光线柔和
              </text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">
                📱
              </text>
              <text class="tip-text">
                拍照技巧：多角度取景，突出乡村特色
              </text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">
                🎯
              </text>
              <text class="tip-text">
                打卡建议：尊重当地文化，文明旅游
              </text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">
                🏆
              </text>
              <text class="tip-text">
                积分奖励：完成打卡可获得专属徽章
              </text>
            </view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="less" scoped>
.page-details {
  height: 100vh;
  padding: 10px;
  background: #fff;
  overflow: auto;

  /* text-indent: 2em; */
  .box {
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      color: #333;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .content {
      font-size: 14px;
      text-indent: 2em;

      .text {
        font-weight: bold;
      }
    }
  }

  .imgbox {
    .img {
      width: 100%;
      height: 190px;
    }

    .textIndent {
      margin-top: 8px;
      text-indent: 2em;
    }
  }

  .undertakings {
    display: flex;
    flex-direction: column;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 38px;
      border-bottom: 1px solid #e5e5e5;
    }
  }

  .activity-list {
    .activity-item {
      display: flex;
      background: #fff;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .activity-image {
        width: 120px;
        height: 100px;
        flex-shrink: 0;
      }

      .activity-info {
        flex: 1;
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .activity-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .activity-desc {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .activity-meta {
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;

          .activity-location {
            margin-left: 16px;
          }
        }

        .activity-status {
          align-self: flex-end;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;

          &.recruiting {
            background: #e8f5e8;
            color: #52c41a;
          }

          &.upcoming {
            background: #fff7e6;
            color: #fa8c16;
          }
        }
      }
    }
  }

  .activity-list {
    .activity-item {
      display: flex;
      background: #fff;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .activity-image {
        width: 120px;
        height: 100px;
        flex-shrink: 0;
      }

      .activity-info {
        flex: 1;
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .activity-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .activity-desc {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .activity-meta {
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;

          .activity-location {
            margin-left: 16px;
          }
        }

        .activity-status {
          align-self: flex-end;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;

          &.recruiting {
            background: #e8f5e8;
            color: #52c41a;
          }

          &.upcoming {
            background: #fff7e6;
            color: #fa8c16;
          }
        }
      }
    }
  }

  .hotspot-page {
    background: #f5f7fa;
    min-height: 100vh;

    .stats-header {
      background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
      padding: 20px 16px;
      color: white;

      .stats-card {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;

        .stats-item {
          text-align: center;

          .stats-number {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .stats-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }

        .stats-divider {
          width: 1px;
          height: 40px;
          background: rgba(255, 255, 255, 0.3);
          margin: 0 40px;
        }
      }

      .header-title {
        text-align: center;

        .title-main {
          display: block;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .title-sub {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }

    .hotspot-list {
      padding: 16px;

      .hotspot-item {
        background: white;
        border-radius: 16px;
        margin-bottom: 16px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .hotspot-image-container {
          position: relative;
          height: 200px;

          .hotspot-image {
            width: 100%;
            height: 100%;
          }

          .hot-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
          }

          .checked-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;

            .checked-text {
              color: white;
              font-size: 18px;
              font-weight: bold;
            }
          }
        }

        .hotspot-content {
          padding: 16px;

          .hotspot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .hotspot-name {
              font-size: 18px;
              font-weight: bold;
              color: #333;
            }

            .checkin-count {
              font-size: 12px;
              color: #ff6b35;
              background: #fff3e0;
              padding: 2px 8px;
              border-radius: 10px;
            }
          }

          .hotspot-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.5;
          }

          .hotspot-tags {
            margin-bottom: 12px;

            .tag {
              display: inline-block;
              background: #e3f2fd;
              color: #1976d2;
              font-size: 12px;
              padding: 4px 8px;
              border-radius: 12px;
              margin-right: 8px;
              margin-bottom: 4px;
            }
          }

          .checkin-time {
            font-size: 12px;
            color: #52c41a;
            margin-bottom: 12px;
          }

          .hotspot-actions {
            display: flex;
            gap: 12px;

            .checkin-btn {
              flex: 1;
              border: none;
              border-radius: 20px;
              padding: 10px 20px;
              font-size: 14px;
              font-weight: bold;

              &.primary {
                background: linear-gradient(135deg, #ff6b6b, #ffa500);
                color: white;
              }

              &.checked {
                background: #e8f5e8;
                color: #52c41a;
              }
            }

            .share-btn {
              background: #f0f0f0;
              color: #666;
              border: none;
              border-radius: 20px;
              padding: 10px 16px;
              font-size: 14px;
            }
          }
        }
      }
    }

    .tips-section {
      margin: 16px;
      background: white;
      border-radius: 16px;
      padding: 20px;

      .tips-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 16px;
        text-align: center;
      }

      .tips-content {
        .tip-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .tip-icon {
            font-size: 20px;
            margin-right: 12px;
          }

          .tip-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
          }
        }
      }
    }
  }
}
</style>
