<route lang="json5" type="page">
    {
      layout: 'tabbar',
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'
import { getAuditAPI } from '@/api/login'
import { del_patientAPI, get_patient_selected, getPatientListAPI } from '@/api/patients'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'

const { warning: showNotify, success: showSuccess } = useToast()

const arr = [
  {
    name: '张三',
    relationship: '本人',
    address: '湖北省/武汉市/洪山区',
    ID: '11111111',
  },
  {
    name: '李四',
    relationship: '家人',
    address: '湖北省/武汉市/洪山区',
    ID: '22222222',
  },
  {
    name: '王五',
    relationship: '亲戚',
    address: '湖北省/武汉市/洪山区',
    ID: '33333333',
  },
]
const active_index = ref(0)
async function set_avtive(index: number) {
  try {
    // 切换就诊人
    await get_patient_selected(index)

    // 重新获取audit值，因为不同就诊人可能对应不同的身份类型
    const auditRes = await getAuditAPI()
    uni.setStorageSync('audit', auditRes.msg)
    console.log('切换就诊人后更新 audit:', auditRes.msg)

    // 刷新列表
    get_list()

    // 显示成功提示
    showSuccess({ msg: '切换成功' })
  }
  catch (error) {
    console.error('切换就诊人失败:', error)
    showNotify({ msg: '切换失败，请重试' })
  }
}
// 返回参数type
interface data_type {
  id: number
  name: string
  gender: number
  relationshipName: string
  province: string
  city: string
  district: string
  memberId: string
  birthDate: string
  selected: number
}
const list_data = ref<data_type[]>([])
function get_list() {
  getPatientListAPI().then((res) => {
    console.log(res)
    list_data.value = res.data as data_type[]
  })
}
onShow(() => {
  get_list()
})
function calculateAge(birthTime: string): number {
  // 尝试解析时间字符串为 Date 对象，若解析失败会返回无效 Date（可根据需求添加错误处理）
  const birthDate: Date = new Date(birthTime)
  const now: Date = new Date()

  const birthYear: number = birthDate.getFullYear()
  const birthMonth: number = birthDate.getMonth()
  const birthDay: number = birthDate.getDate()
  const nowYear: number = now.getFullYear()
  const nowMonth: number = now.getMonth()
  const nowDay: number = now.getDate()

  let age: number = nowYear - birthYear

  if (nowMonth < birthMonth || (nowMonth === birthMonth && nowDay < birthDay)) {
    age--
  }

  return age < 0 ? 0 : age
}
// 添加就诊人
function add_patient() {
  uni.navigateTo({
    url: '/pages-sub/Patients/index',
  })
}
// 跳转到修改
function to_add(id: number) {
  console.log(id)
  uni.navigateTo({
    url: `/pages-sub/Patients/index?id=${id}`,
  })
}
// 删除就诊人
async function handleAction(id: any) {
  if (list_data.value.length <= 1) {
    showNotify({ msg: '至少保留一位就诊人' })
    return
  }
  uni.showModal({
    title: '提示',
    content: '确定要删除该就诊人吗？',
    success: async (res) => {
      if (res.confirm) {
        await del_patientAPI(id)
        showSuccess({ msg: '删除成功' })
        get_list()
      }
    },
  })
}
</script>

<template>
  <view class="page px-[16px]">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      就诊人管理
    </FgNavbar>
    <view class="my-[6px]">
      <view
        v-for="item in list_data"
        :key="item.id"
        class="my-[6px]"
      >
        <wd-swipe-action>
          <template #right>
            <view class="action">
              <view
                class="button"
                style="
                  background: #fd5844;
                  display: flex;
                  text-align: center;
                  align-items: center;
                "
                @click="handleAction(item.id)"
              >
                删除
              </view>
            </view>
          </template>
          <view class="patients_item p-[16px]" @click="to_add(item.id)">
            <view class="flex items-center justify-between py-[8x]">
              <view class="">
                <view class="flex">
                  <view class="font-600 line-height-[24px]">
                    {{ item.name }}
                  </view>
                  <view
                    class="ml-[10px]"
                    :class="{
                      my_button: item.relationshipName === '本人',
                      family_button: item.relationshipName === '家人',
                      relative_button: item.relationshipName !== '本人' && item.relationshipName !== '家人',
                    }"
                  >
                    {{ item.relationshipName }}
                  </view>
                </view>
                <view class="foot_id py-[4px]">
                  ID : {{ item.id }}
                </view>
              </view>

              <view v-if="item.selected === 1" class="now_item">
                当前就诊人
              </view>
              <view
                v-else
                class="now_item"
                style="color: white; background-color: #007fee"
                @click.stop="set_avtive(item.id)"
              >
                设为当前
              </view>
            </view>
            <view class="address mt-[16px] flex gap-[8px]">
              <span>{{
                item.gender === 0 ? "未知" : item.gender === 1 ? "男" : "女"
              }}</span>
              <span class="">{{ calculateAge(item.birthDate) }}岁</span>
              <span>{{ `${item.province}/${item.city}/${item.district}` }}</span>
            </view>
          </view>
        </wd-swipe-action>
      </view>
      <view class="h-[160rpx]" />
    </view>
    <view class="footer">
      <view class="footer_btn" @click="add_patient">
        添加就诊人
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  // padding-bottom: 160rpx; // 新增，避免底部按钮被遮挡
}
.foot_id {
  color: #333;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
}
.now_item {
  display: flex;
  height: 32rpx;
  padding: 12rpx 32rpx;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  background: rgba(0, 127, 238, 0.1);
  color: #007fee;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
}
.patients_item {
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
}
.address {
  color: #666;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
}
.my_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #cf9358; // 本人
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #fcf1e4; // 本人
}
.family_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #273554; // 家人
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #e9ebee; // 家人
}
.relative_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #273554; // 亲戚/其他：灰色
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #e9ebee; // 亲戚/其他：浅灰
}
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 0 48rpx 0;
  background: #fff;
  border-bottom-left-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
  box-shadow: 0 -8rpx 24rpx 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  z-index: 10;

  .footer_btn {
    width: 90vw;
    height: 88rpx;
    background: #1890ff;
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    text-align: center;
    line-height: 88rpx;
    border: none;
  }
}
.action {
  height: 100%;
}
.button {
  display: inline-block;
  padding: 0 11px;
  height: 100%;
  color: white;
  line-height: 42px;
}
</style>
