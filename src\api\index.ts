import { http } from '@/utils/http'
// 获取健康知识tabs栏
export function getHealthKnowledgeTabsAPI() {
  return http.get('/h5/disease/healthKnowledge/getTab')
}
// 查询健康知识文章列表
export function getHealthKnowledgeListAPI(params: { pageNum: number, pageSize: number, articleType: string }) {
  return http.get('/h5/disease/healthKnowledge/list', params)
}
// 获取健康知识文章详细信息
export function getHealthKnowledgeDetailAPI(id: string) {
  return http.get(`/h5/disease/healthKnowledge/${id}`)
}
// 获取健康知识留言列表
export function getHealthKnowledgeCommentListAPI(params: { knowledgeId: string, pageNum: number, pageSize: number }) {
  return http.get('/h5/disease/healthKnowledge/commentlist', params)
}
// 新增健康知识留言
export function addHealthKnowledgeCommentAPI(data: { knowledgeId: string, content: string }) {
  return http.post('/h5/disease/healthKnowledge/addComment', data)
}
// 是否关注公众号
export function isFollowWechatAPI(data: { code: string }) {
  return http.post('/h5/member/code', data)
}


