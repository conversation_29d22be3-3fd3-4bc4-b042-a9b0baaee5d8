<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { onUnload } from '@dcloudio/uni-app'
import { nextTick } from 'vue'
import { useToast } from 'wot-design-uni'
import { addPatientAPI, get_patient_info, get_patient_selected, getConfigAPI, put_patient_info } from '@/api/patients'
import { useColPickerData } from '@/hooks/useColPickerData'
import { useUserStore } from '@/store'

const { success: showSuccess, warning: showNotify } = useToast()
const edit_id = ref()
const userStore = useUserStore()

// 添加一个标记变量
const isFromLogin = ref(false)

// 表单数据
const form = ref()
const initialModel = {
  name: '', // 姓名
  gender: '', // 0-未知 1-男 2-女
  relationship: '', // 与患者关系
  birthDate: 0, // 出生年月
  contactPhone: '', // 联系方式
  province: '', // 省份/直辖市
  city: '', // 城市
  district: '', // 区/县
  lifestyleHabits: {
    // 生活习惯
    exercise: '', // 运动情况
    dietPreference: '', // 饮食偏好
    sleepQuality: '', // 睡眠情况
    smokingStatus: '', // 吸烟情况
    drinkingFrequency: '', // 饮酒情况
  },
  healths: [
    // 健康信息
    { patientId: '', // 慢性病父id
      chronicDiseaseId: '', // 慢性病名称
      diagnosisDate: 0 }, // 使用0作为初始值，类似出生日期
  ],
  medications: [
    // 服用药物
    {
      medicineName: '', // 药物名称
      frequency: '', // 药物频次
    },
  ],
}

const model = reactive({ ...initialModel })
const value = ref()
// 选择与患者关系
function handleConfirm({ value }) {
  // console.log(value, 'value患者关系')
}
// 出生年月
function handleConfirmDateBirth({ value }) {
  model.birthDate = new Date(value).getTime()
}

// 确诊时间确认处理
function handleConfirmDiagnosisDate({ value }, index) {
  // 直接设置时间戳，让组件自己处理显示
  model.healths[index].diagnosisDate = value
}

// 慢性病选择确认处理
function handleChronicDiseaseConfirm(value, selectedItems, idx) {
  // 确保索引有效
  if (idx >= 0 && idx < model.healths.length) {
    model.healths[idx].chronicDiseaseId = value[1] || ''
    console.log(`健康信息${idx + 1}选择的慢性病ID:`, value[1])
    console.log('当前慢性病选择值:', chronicDiseaseValue.value[idx])
  }
}
// 所在地区
const { colPickerData, findChildrenByCode } = useColPickerData()
const location = ref([])
const area = ref<any[]>([
  colPickerData.map((item) => {
    return {
      value: item.value,
      label: item.text,
    }
  }),
])
// 列改变
function columnChange({ selectedItem, resolve, finish }) {
  const areaData = findChildrenByCode(colPickerData, selectedItem.value)
  if (areaData && areaData.length) {
    resolve(
      areaData.map((item) => {
        return {
          value: item.value,
          label: item.text,
        }
      }),
    )
  }
  else {
    finish()
  }
}
// 递归查找编码对应的中文名
function getAreaLabelByCode(data, code) {
  for (const item of data) {
    if (item.value === code) {
      return item.text
    }
    if (item.children) {
      const found = getAreaLabelByCode(item.children, code)
      if (found)
        return found
    }
  }
  return ''
}
function getAreaCodesByNames(data, provinceName, cityName, districtName) {
  const result = ['', '', '']

  function findLabels(nodes, level = 0) {
    for (const item of nodes) {
      // 修正：根据名称查找，而不是编码
      if (level === 0 && item.text === provinceName) {
        result[0] = item.value
        // 如果有子节点且需要查找下一级（城市）
        if (item.children && cityName) {
          // 特殊处理：如果城市名称和省份名称相同，需要进一步判断
          if (cityName === provinceName) {
            // 在子节点中查找名称相同但编码不同的城市
            for (const cityItem of item.children) {
              if (cityItem.text === cityName) {
                result[1] = cityItem.value
                // 如果有区县名称，继续查找区县
                if (districtName && cityItem.children) {
                  findLabels(cityItem.children, 2)
                }
                break // 找到城市后跳出循环
              }
            }
          }
          else {
            // 城市名称不同，正常递归查找
            findLabels(item.children, 1)
          }
        }
        return // 找到省份后提前返回
      }
      else if (level === 1 && item.text === cityName) {
        result[1] = item.value
        // 如果有子节点且需要查找下一级（区县）
        if (item.children && districtName) {
          findLabels(item.children, 2)
        }
        return // 找到城市后提前返回
      }
      else if (level === 2 && item.text === districtName) {
        result[2] = item.value
        return // 找到区县后提前返回
      }

      // 继续递归查找子节点
      if (item.children) {
        findLabels(item.children, level)
      }
    }
  }

  findLabels(data)
  return result
}
// 地址确定
function handleConfirmLocation({ value }) {
  // value: [{text, value}, ...]
  console.log(value)
  model.province = getAreaLabelByCode(colPickerData, value[0]) || ''
  model.city = getAreaLabelByCode(colPickerData, value[1]) || ''
  model.district = getAreaLabelByCode(colPickerData, value[2]) || ''
}

// 默认值，单位rpx或px，看你组件实际单位
const navbarHeight = ref(88)

// 配置信息字典数据
interface DictItem {
  dictLabel: string
  dictValue: string
}
interface ConfigData {
  exerciseDict: DictItem[]
  dietPreferenceDict: DictItem[]
  sleepDurationDict: DictItem[]
  smokingStatusDict: DictItem[]
  drinkingFrequencyDict: DictItem[]
  relationshipDict: DictItem[]
  chronicDiseases: { diseaseName: string }[]
}
const exerciseOptions = ref<{ label: string, value: string }[]>([])
const dietPreferenceOptions = ref<{ label: string, value: string }[]>([])
const sleepDurationOptions = ref<{ label: string, value: string }[]>([])
const smokingStatusOptions = ref<{ label: string, value: string }[]>([])
const drinkingFrequencyOptions = ref<{ label: string, value: string }[]>([])
const relationshipOptions = ref<{ label: string, value: string }[]>([])
const genderOptions = ref<{ label: string, value: number }[]>([
  { label: '男', value: 1 },
  { label: '女', value: 2 },
])
const chronicDiseaseOptions = ref<string[]>([])

// 2级慢性病联动 columns 和 value
const chronicDiseaseColumns = ref<Array<Array<any>>>([]) // 每条健康信息一组columns
const chronicDiseaseValue = ref<string[][]>([['', '']]) // 每条健康信息一个二维数组

// 初始化慢性病 columns
function initChronicDiseaseColumns(data, healths = []) {
  // 一级
  const firstLevel: Array<{ label: string, value: string, children?: any[] }> = [{ label: '请选择', value: '', children: [] }]
  if (data && data.length) {
    firstLevel.push(
      ...data.map(item => ({
        label: item.diseaseName,
        value: item.id,
        children: item.children || [],
      })),
    )
  }
  // 初始化每条健康信息的columns
  chronicDiseaseColumns.value = healths.length
    ? healths.map((h) => {
        // 找到当前健康信息的parentId对应的children
        const parent = firstLevel.find(item => item.value === h[0])
        const secondLevel = [{ label: '请选择', value: '' }]
        if (parent && parent.children && parent.children.length) {
          secondLevel.push(
            ...parent.children.map(item => ({
              label: item.diseaseName,
              value: item.id,
            })),
          )
        }
        return [firstLevel, secondLevel]
      })
    : [[firstLevel, [{ label: '请选择', value: '' }]]]
}

// column-change 方法
function onChronicDiseaseColumnChange(pickerView, selectedItems, columnIndex, resolve, idx) {
  if (columnIndex === 0) {
    const children = selectedItems[0]?.children || []
    const secondLevel = [{ label: '请选择', value: '' }]
    if (children.length) {
      secondLevel.push(
        ...children.map(item => ({
          label: item.diseaseName,
          value: item.id,
        })),
      )
    }
    // 重置当前条目的二级选项
    chronicDiseaseColumns.value[idx][1] = secondLevel
    chronicDiseaseValue.value[idx][1] = ''
  }
  resolve()
}

// 递归处理所有慢性病（含子节点）
function flattenChronicDiseases(list) {
  let result = []
  for (const item of list) {
    result.push({
      label: item.diseaseName,
      value: item.id, // 这里用id
      children: item.children || [], // 保证children字段存在
    })
    if (item.children && item.children.length) {
      result = result.concat(flattenChronicDiseases(item.children))
    }
  }
  return result
}

// 全局缓存慢性病树数据，供add/remove时用
let globalChronicDiseaseTree = []

onMounted(async () => {
  // 以uni-app为例
  // 获取系统信息
  uni.getSystemInfo({
    success: (res) => {
      // 这里可以根据 res.statusBarHeight、导航栏自定义高度等计算
      // 假设你的导航栏高度 = 状态栏高度 + 44px
      navbarHeight.value = (res.statusBarHeight || 0) + 44
    },
  })
  // 获取配置信息
  const res = await getConfigAPI()
  // console.log(res.data, 'res')

  const data = res.data as ConfigData
  // 运动情况
  exerciseOptions.value = data.exerciseDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 饮食偏好
  dietPreferenceOptions.value = data.dietPreferenceDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 睡眠情况
  sleepDurationOptions.value = data.sleepDurationDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 吸烟情况
  smokingStatusOptions.value = data.smokingStatusDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 饮酒频率
  drinkingFrequencyOptions.value = data.drinkingFrequencyDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 与患者关系
  relationshipOptions.value = data.relationshipDict.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
  // 慢性病
  chronicDiseaseOptions.value = flattenChronicDiseases(data.chronicDiseases)
  globalChronicDiseaseTree = data.chronicDiseases // 缓存慢性病树
  // initChronicDiseaseColumns(data.chronicDiseases)
  // 初始化健康信息选择器数组
  if (!chronicDiseaseValue.value.length)
    chronicDiseaseValue.value = model.healths.map(() => ['', ''])
  // 初始化columns
  initChronicDiseaseColumns(data.chronicDiseases, chronicDiseaseValue.value)
})

function addHealthInfo() {
  if (model.healths.length < 3) {
    model.healths.push({ chronicDiseaseId: '', diagnosisDate: 0, patientId: '' }) // 使用0作为初始值
    chronicDiseaseValue.value.push(['', ''])
    // 新增：为新添加的健康信息设置独立的columns
    initChronicDiseaseColumns(globalChronicDiseaseTree, chronicDiseaseValue.value)
  }
}

function removeHealthInfo(index: number) {
  console.log('删除前的健康信息:', JSON.parse(JSON.stringify(model.healths)))
  console.log('删除前的慢性病选择值:', JSON.parse(JSON.stringify(chronicDiseaseValue.value)))
  console.log('要删除的索引:', index)

  // 删除对应的数据
  model.healths.splice(index, 1)
  chronicDiseaseValue.value.splice(index, 1)

  // 重新初始化慢性病选择器的列数据
  initChronicDiseaseColumns(globalChronicDiseaseTree, chronicDiseaseValue.value)

  // 强制更新视图，确保数据同步
  nextTick(() => {
    console.log('删除后的健康信息:', JSON.parse(JSON.stringify(model.healths)))
    console.log('删除后的慢性病选择值:', JSON.parse(JSON.stringify(chronicDiseaseValue.value)))
    console.log('删除后的慢性病列数据长度:', chronicDiseaseColumns.value.length)
  })
}

// 新增：服用药物动态数组及方法
function addMedicineInfo() {
  if (model.medications.length < 3) {
    model.medications.push({ medicineName: '', frequency: '' })
  }
}

// 删除
function removeMedicineInfo(index: number) {
  model.medications.splice(index, 1)
}
function validateHealths(healths) {
  if (!healths || !healths.length) {
    showNotify({ msg: '请至少填写一条健康信息' })
    return false
  }
  const ids = new Set()
  for (let i = 0; i < healths.length; i++) {
    const h = healths[i]
    if (!h.chronicDiseaseId) {
      showNotify({ msg: `第${i + 1}条健康信息未选择慢性病` })
      return false
    }
    if (ids.has(h.chronicDiseaseId)) {
      showNotify({ msg: `慢性病不能重复选择（第${i + 1}条）` })
      return false
    }
    ids.add(h.chronicDiseaseId)
    // 检查确诊时间是否有效
    // 如果是时间戳格式，检查是否为有效的正数时间戳
    // 如果是字符串格式，检查是否为有效的日期字符串
    if (typeof h.diagnosisDate === 'number') {
      // 时间戳格式：检查是否为有效的正数时间戳（排除0，因为0是默认值）
      if (h.diagnosisDate == null || h.diagnosisDate <= 0) {
        showNotify({ msg: `第${i + 1}条健康信息未填写确诊时间` })
        return false
      }
    }
    else {
      // 字符串格式：检查是否为空或格式不正确
      if (!h.diagnosisDate || h.diagnosisDate === '') {
        showNotify({ msg: `第${i + 1}条健康信息未填写确诊时间` })
        return false
      }
      // 校验日期格式
      if (!/^\d{4}-\d{2}-\d{2}$/.test(h.diagnosisDate)) {
        showNotify({ msg: `第${i + 1}条确诊时间格式不正确` })
        return false
      }
    }
  }
  return true
}
function validateRequiredFields(model) {
  if (!model.name) {
    showNotify({ msg: '请输入姓名' })
    return false
  }
  if (!model.gender) {
    showNotify({ msg: '请选择性别' })
    return false
  }
  if (!model.relationship) {
    showNotify({ msg: '请选择与患者关系' })
    return false
  }
  if (model.birthDate == null || model.birthDate === '') {
    showNotify({ msg: '请选择出生年月' })
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(model.contactPhone)) {
    showNotify({ msg: '请输入正确的手机号' })
    return false
  }
  if (!model.province || !model.city || !model.district) {
    showNotify({ msg: '请选择所在地区' })
    return false
  }
  return true
}
// 保存
async function handleSave() {
  console.log('保存前的原始健康信息:', JSON.parse(JSON.stringify(model.healths)))
  console.log('保存前的慢性病选择值:', JSON.parse(JSON.stringify(chronicDiseaseValue.value)))

  const data = formatModelData()
  console.log('最终提交数据:', JSON.parse(JSON.stringify(data)))

  // 先校验主表单
  if (!validateRequiredFields(model))
    return
  // 再校验健康信息
  if (!validateHealths(data.healths))
    return
  // showSuccess({ msg: '保存成功' }) // 移动到具体分支
  // 这里可以调用API提交 data
  // uni.switchTab({ url: '/pages/index/index' })
  if (edit_id.value) {
    // put_patient_info
    const res = await put_patient_info({ id: edit_id.value, ...data })
    if (res.code === 200) {
      showSuccess({
        msg: '保存成功',
      })
      // 清空表单
      Object.assign(model, JSON.parse(JSON.stringify(initialModel)))
      // 跳转前延迟 1 秒
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  }
  else {
    const res = await addPatientAPI(data)
    if (res.code === 200) {
      showSuccess({
        msg: '保存成功',
      })
      // 清空表单
      Object.assign(model, JSON.parse(JSON.stringify(initialModel)))
      // 跳转前延迟 1 秒
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' })
      }, 2000)
    }
  }
}
// 转换时间
function formatDate(ts) {
  // 修复：不能使用!ts，因为时间戳0是有效的1970-01-01
  if (ts == null || ts === '' || ts < 0)
    return ''
  const d = new Date(ts)
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${y}-${m}-${day}`
}
function formatModelData() {
  // 处理出生年月
  const birthDate = formatDate(model.birthDate)

  // 处理性别
  const gender
    = typeof model.gender === 'string' ? Number(model.gender) : model.gender

  // 处理健康信息
  const healths = model.healths?.map((h, index) => {
    // 确保 chronicDiseaseId 是有效的数字
    let chronicDiseaseId: number | null = null
    if (h.chronicDiseaseId) {
      const numericId = Number(h.chronicDiseaseId)
      if (!Number.isNaN(numericId)) {
        chronicDiseaseId = numericId
      }
      else {
        console.warn(`健康信息${index + 1}的慢性病ID无效:`, h.chronicDiseaseId)
      }
    }

    return {
      ...h,
      chronicDiseaseId,
      // 修复：不能排除时间戳0，因为它是有效的1970-01-01
      diagnosisDate: h.diagnosisDate != null && h.diagnosisDate >= 0 ? formatDate(h.diagnosisDate) : undefined,
    }
  }) // 不在这里过滤，让验证函数来处理

  // 组装最终数据
  const result = {
    ...model,
    birthDate,
    gender,
    healths,
  }
  return result
}
const isFollowWechat = ref(false)
// 控制返回按钮显示
const showBackButton = ref(true)
onLoad((options) => {
  if (options.form === 'login') {
    isFromLogin.value = true // 只标记来源，不立即清空
  }
  isFollowWechat.value = !uni.getStorageSync('isFollowWechat')
  if (options.id) {
    edit_id.value = options.id
    get_patient_info(options.id).then((res: any) => {
      console.log(res.data)
      for (const i in res.data) {
        model[i] = res.data[i]
      }
      // 处理健康信息的日期格式
      if (res.data.healths && res.data.healths.length) {
        res.data.healths.forEach((health, index) => {
          if (health.diagnosisDate) {
            // 如果是字符串格式，转换为时间戳
            if (typeof health.diagnosisDate === 'string') {
              model.healths[index].diagnosisDate = new Date(health.diagnosisDate).getTime()
            }
            // 如果已经是数字格式，直接使用
            else if (typeof health.diagnosisDate === 'number') {
              model.healths[index].diagnosisDate = health.diagnosisDate
            }
          }
        })
      }
      chronicDiseaseValue.value = res.data.healths.map(item => [item.chronicDisease.parentId, item.chronicDisease.id])
      // 新增：为每条健康信息设置独立的columns
      initChronicDiseaseColumns(globalChronicDiseaseTree, chronicDiseaseValue.value)
      location.value = getAreaCodesByNames(colPickerData, model.province, model.city, model.district)
      area.value = [
        colPickerData.map((item) => {
          return {
            value: item.value,
            label: item.text,
          }
        }),
        findChildrenByCode(colPickerData, location.value[0])!.map((item) => {
          // console.log(item)
          return {
            value: item.value,
            label: item.text,
          }
        }),
        findChildrenByCode(colPickerData, location.value[1])!.map((item) => {
          // console.log(item)
          return {
            value: item.value,
            label: item.text,
          }
        }),
      ]
      console.log(123213, location)
      console.log(chronicDiseaseValue.value)
      // model = {
      //   name: '', // 姓名
      //   gender: '', // 0-未知 1-男 2-女
      //   relationship: '', // 与患者关系
      //    birthDate: 0, // 出生年月
      //   contactPhone: '', // 联系方式
      //   province: '', // 省份/直辖市
      //   city: '', // 城市
      //   district: '', // 区/县
      //   lifestyleHabits: {
      //     // 生活习惯
      //     exercise: '', // 运动情况
      //     dietPreference: '', // 饮食偏好
      //     sleepQuality: '', // 睡眠情况
      //     smokingStatus: '', // 吸烟情况
      //     drinkingFrequency: '', // 饮酒情况
      //   },
      //   healths: [
      //     // 健康信息
      //     {
      //       chronicDiseaseId: '', // 慢性病名称
      //       diagnosisDate: '', // 确诊时间
      //     },
      //   ],
      //   medications: [
      //     // 服用药物
      //     {
      //       medicineName: '', // 药物名称
      //       frequency: '', // 药物频次
      //     },
      //   ],
      // }
    })
  }
})

function setAsCurrentPatient() {
  if (!edit_id.value)
    return
  get_patient_selected(edit_id.value).then((res) => {
    if (res.code === 200) {
      showSuccess({ msg: '切换成功' })
      setTimeout(() => {
        uni.navigateBack()
      }, 800)
    }
  })
}

// 清空用户信息的通用函数
function clearUserInfoIfNeeded() {
  if (isFromLogin.value) {
    // 只有从登录页进来才清空用户信息
    userStore.removeUserInfo()
    console.log('已清除用户信息')
  }
}

// 添加返回按钮点击处理函数
function handleBack() {
  clearUserInfoIfNeeded()
  uni.navigateBack({
    fail() {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    },
  })
}

// 页面卸载时的处理（包括左滑返回）
onUnload(() => {
  clearUserInfoIfNeeded()
})
</script>

<template>
  <view class="page">
    <FgNavbar
      class="navbar-fixed"
      :left-arrow="showBackButton"
      left-text=""
      :custom-click-left="true"
      @click-left="handleBack"
    >
      就诊人信息
    </FgNavbar>
    <view class="content" :style="{ marginTop: `${navbarHeight}px` }">
      <!-- <wd-notice-bar v-if="isFollowWechat" text="关注公众号，接收通知消息！" type="info" :scrollable="false" prefix="check-outline" /> -->
      <!-- 标题 -->
      <view class="title title_top">
        就诊人信息
        <!-- 新增：仅编辑时显示切换按钮 -->
        <view v-if="edit_id" class="switch-btn" @click="setAsCurrentPatient">
          切换为当前就诊人
        </view>
      </view>
      <!-- 表单 -->
      <wd-form ref="form" :model="model">
        <wd-cell-group border>
          <!-- 姓名 -->
          <wd-input
            v-model="model.name"
            label="姓名"
            prop="name"
            clearable
            :maxlength="15"
            placeholder="请输入姓名"
            :rules="[{ required: true, message: '请输入姓名' }]"
          />
          <!-- 性别 -->
          <wd-picker
            v-model="model.gender"
            :columns="genderOptions"
            label="性别"
            placeholder="请选择性别"
            required
            :rules="[{ required: true, message: '请选择性别' }]"
            @confirm="handleConfirm"
          />
          <!-- 与患者关系 -->
          <wd-picker
            v-model="model.relationship"
            :columns="relationshipOptions"
            label="与患者关系"
            placeholder="请选择与患者关系"
            required
            @confirm="handleConfirm"
          />
          <!-- 出生年月 选择 -->
          <wd-datetime-picker
            v-model="model.birthDate"
            type="date"
            label="出生年月"
            :min-date="new Date(1950, 0, 1).getTime()"
            :max-date="new Date().getTime()"
            placeholder="请选择出生年月"
            required
            @confirm="handleConfirmDateBirth"
          />
          <!-- 联系方式 -->
          <wd-input
            v-model="model.contactPhone"
            label="联系方式"
            prop="number"
            :maxlength="11"
            clearable
            placeholder="请输入联系方式"
            :rules="[{ required: true, message: '请输入联系方式' }]"
          />
          <!-- 所在地区 -->
          <wd-col-picker
            v-model="location"
            label="选择地址"
            :columns="area"
            :column-change="columnChange"
            placeholder="请选择所在地区"
            required
            @confirm="handleConfirmLocation"
          />
        </wd-cell-group>
      </wd-form>
      <!-- 标题 -->
      <view class="title">
        生活习惯
      </view>
      <!-- 生活习惯表单 -->
      <wd-form ref="form" :model="model">
        <wd-cell-group border>
          <!-- 运动情况 -->
          <view style="padding: 0 0 0 24rpx;">
            <wd-picker
              v-model="model.lifestyleHabits.exercise"
              :columns="exerciseOptions"
              label="运动情况"
              placeholder="请选择运动情况"
              @confirm="handleConfirm"
            />
            <!-- 饮食偏好 -->
            <wd-picker
              v-model="model.lifestyleHabits.dietPreference"
              :columns="dietPreferenceOptions"
              label="饮食偏好"
              placeholder="请选择饮食偏好"
              @confirm="handleConfirm"
            />
            <!-- 睡眠情况 -->
            <wd-picker
              v-model="model.lifestyleHabits.sleepQuality"
              :columns="sleepDurationOptions"
              label="睡眠情况"
              placeholder="请选择睡眠情况"
              @confirm="handleConfirm"
            />
            <!-- 吸烟情况 -->
            <wd-picker
              v-model="model.lifestyleHabits.smokingStatus"
              :columns="smokingStatusOptions"
              label="吸烟情况"
              placeholder="请选择吸烟情况"
              @confirm="handleConfirm"
            />
            <!-- 饮酒情况 -->
            <wd-picker
              v-model="model.lifestyleHabits.drinkingFrequency"
              :columns="drinkingFrequencyOptions"
              label="饮酒情况"
              placeholder="请选择饮酒情况"
              @confirm="handleConfirm"
            />
          </view>
        </wd-cell-group>
      </wd-form>
      <!-- 标题 -->
      <view class="title health_title">
        健康信息
      </view>
      <!-- 健康信息表单 -->
      <wd-form ref="form" :model="model">
        <wd-cell-group border>
          <template v-for="(info, idx) in model.healths" :key="idx">
            <!-- 标题单独放外面 -->
            <view class="title_index">
              健康信息{{ idx + 1 }}
            </view>
            <!-- 表单项放在 cell-group 里 -->
            <wd-cell-group border>
              <view style="padding: 0 0 0 24rpx;">
                <wd-picker
                  v-model="chronicDiseaseValue[idx]"
                  :columns="chronicDiseaseColumns[idx]"
                  label="慢性病名称"
                  placeholder="请选择慢性病名称"
                  :column-change="(pickerView, selectedItems, columnIndex, resolve) => onChronicDiseaseColumnChange(pickerView, selectedItems, columnIndex, resolve, idx)"
                  @confirm="({ value, selectedItems }) => handleChronicDiseaseConfirm(value, selectedItems, idx)"
                />
                <wd-datetime-picker
                  v-model="info.diagnosisDate"
                  type="date"
                  label="确诊时间"
                  placeholder="请选择确诊时间"
                  :min-date="new Date(1900, 0, 1).getTime()"
                  :max-date="new Date().getTime()"
                  @confirm="({ value }) => handleConfirmDiagnosisDate({ value }, idx)"
                />
              </view>
              <!-- 其他按钮等 -->
            </wd-cell-group>
            <view class="btn_group">
              <!-- 删除按钮 -->
              <view
                v-if="model.healths.length > 1"
                class="btn-del"
                @click="removeHealthInfo(idx)"
              >
                删除
              </view>
              <!-- 添加按钮 -->
              <view
                v-if="
                  model.healths.length < 3 && idx === model.healths.length - 1
                "
                class="btn-add"
                @click="addHealthInfo"
              >
                添加
              </view>
            </view>
          </template>
        </wd-cell-group>
      </wd-form>
      <!-- 标题 -->
      <view class="title" style="padding: 0 0 0 22rpx;">
        服用药物
      </view>
      <!-- 服用药物 -->
      <wd-form ref="form" :model="model">
        <wd-cell-group border>
          <template v-for="(info, idx) in model.medications" :key="idx">
            <view class="title_index">
              服用药物{{ idx + 1 }}
            </view>
            <!-- 药物名称 -->
            <view style="padding: 0 0 0 24rpx;">
              <wd-input
                v-model="info.medicineName"
                label="药物名称"
                label-width="220rpx"
                prop="medicineName"
                clearable
                placeholder="请输入药物名称"
              />
              <!-- 药物频次 -->
              <wd-input
                v-model="info.frequency"
                label="药物频次"
                label-width="220rpx"
                prop="frequency"
                clearable
                placeholder="请输入药物频次"
              />
            </view>
            <view class="btn_group">
              <!-- 删除按钮 -->
              <view
                v-if="
                  (model.medications.length === 2 && idx === 1)
                    || (model.medications.length === 3 && idx === 2)
                "
                class="btn-del"
                @click="removeMedicineInfo(idx)"
              >
                删除
              </view>
              <!-- 添加按钮 -->
              <view
                v-if="
                  model.medications.length < 3
                    && idx === model.medications.length - 1
                "
                class="btn-add"
                @click="addMedicineInfo"
              >
                添加
              </view>
            </view>
          </template>
        </wd-cell-group>
      </wd-form>
      <view class="footer-btn">
        <button class="save-btn" @click="handleSave">
          保存
        </button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  min-height: 100vh;

  .navbar-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 1000;
  }

  .content {
    padding-top: 7rpx;
    padding-bottom: 300rpx; // 新增，底部留白

    .title {
      color: #000;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 700;
      margin: 16rpx 32rpx 16rpx 32rpx;
      display: flex;
      align-items: center;
      padding: 0 0 0 22rpx;
    }

    .title_top {
      color: #000;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 700;
      margin: 16rpx 32rpx 16rpx 32rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .title_index {
      padding: 16rpx 32rpx;
      color: #000;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      margin-left: 20rpx;
    }

    .health_title {
      position: relative;

      &::before {
        position: absolute;
        left: 0;
        top: 2px;
        content: '*';
        font-size: 36rpx;
        line-height: 1.1;
        color: #fa4350;
        margin-right: 10rpx;
        font-weight: 500;
      }
    }

    .btn_group {
      display: flex;
      justify-content: flex-end;
      margin-right: 32rpx;
      margin-top: 24rpx;
      margin-bottom: 24rpx;

      .btn-del {
        padding: 14rpx 24rpx;
        border-radius: 200rpx;
        margin-right: 24rpx;
        margin-bottom: 24rpx;
        border: 1px solid #bfbfbf;
        color: #595959;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 24rpx;
        background: #fff;
        box-sizing: border-box;
        height: 52rpx;
        line-height: 26rpx;
      }

      .btn-add {
        padding: 14rpx 24rpx;
        border-radius: 200rpx;
        margin-right: 24rpx;
        margin-bottom: 24rpx;
        background: #3d7dff;
        color: #fff;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 24rpx;
        box-sizing: border-box;
        height: 52rpx;
        line-height: 26rpx;
      }
    }
  }
  .footer-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 10;
    padding: 32rpx 32rpx 54rpx 32rpx;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.03);
    .save-btn {
      width: 100%;
      height: 80rpx;
      background: #3d7dff;
      color: #fff;
      border-radius: 40rpx;
      font-size: 32rpx;
      border: none;
    }
  }
}

.form-item {
  display: flex;
  align-items: center;
  margin: 16rpx 0;

  .label {
    width: 360rpx;
    font-size: 28rpx;
    color: #333;
    margin-left: 28rpx;

    &.required::before {
      left: 0;
      top: 2px;
      content: '*';
      font-size: 36rpx;
      line-height: 1.1;
      color: #fa4350;
      margin-right: 10rpx;
    }
  }
}

.gender-radio-wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.switch-btn {
  background: #7c3aed;
  color: #fff;
  border-radius: 12rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

:deep(.wd-picker__label) {
  margin-right: 12rpx !important;
}
:deep(.wd-picker__value) {
  margin-left: 20rpx !important;
}
</style>
