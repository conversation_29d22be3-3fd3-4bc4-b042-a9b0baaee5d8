<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { ref } from 'vue'
import { addHealthKnowledgeCommentAPI, getHealthKnowledgeCommentListAPI, getHealthKnowledgeDetailAPI } from '@/api/index'
import { checkUserLogin } from '@/utils'

// 文章数据
const articleData = ref({
  title: '', // 标题
  sendName: '', // 发布人
  publishTime: '', // 发布时间
  coverImage: '', // 封面
  articleComment: '', // 文章内容
  allowComment: 1, // 新增，默认允许留言
  delFlag: 0, // 删除标志：0->未删除；1->已删除
})

// 动态处理富文本图片样式，防止图片撑破页面
function fixImgStyle(html: string) {
  return html ? html.replace(/<img/gi, '<img style="max-width:100%;height:auto;display:block;"') : ''
}
// 模拟留言数据
const comments = ref([])
const id = ref('')
const commentInput = ref('')
const replyInput = ref('')
const replyingTo = ref<number | null>(null) // 当前正在回复的评论ID
const inputBoxBottom = ref(0) // 输入框距离底部的距离

const BASE_URL = import.meta.env.VITE_IMG_URL

function startReply(commentId: number) {
  replyingTo.value = commentId
  replyInput.value = ''
}

function cancelReply() {
  replyingTo.value = null
  replyInput.value = ''
}

function sendReply() {
  if (!replyInput.value.trim() || replyingTo.value === null)
    return

  const comment = comments.value.find(c => c.id === replyingTo.value)
  if (comment) {
    comment.reply = {
      replyName: '张某',
      eauthorReply: 0,
      replyAvatar: '/static/images/avatar.jpg',
      replyTime: '',
      replyContent: replyInput.value,
      isAuthor: true,
    }
  }

  replyingTo.value = null
  replyInput.value = ''
}
// 获取详情
async function getDetail(id) {
  const res = await getHealthKnowledgeDetailAPI(id)
  const data = res.data as any
  // 动态处理图片样式
  if (data && data.articleComment) {
    data.articleComment = fixImgStyle(data.articleComment)
  }
  articleData.value = data || {}
}
// 获取留言列表
const pages = ref({
  pageNum: 1,
  pageSize: 10,
  knowledgeId: '',
})
const totalComments = ref(0) // 总留言数
async function getCommentList(id: string) {
  pages.value.knowledgeId = id
  const res = await getHealthKnowledgeCommentListAPI(pages.value)
  // 转换结构
  comments.value = (res as any).rows.map((item: any) => ({
    ...item,
    reply: item.replyContent
      ? {
          replyName: item.replyName,
          eauthorReply: item.eauthorReply,
          replyAvatar: item.replyAvatar,
          replyTime: item.replyTime,
          replyContent: item.replyContent,
          isAuthor: item.eauthorReply === 0,
        }
      : null,
  }))
  totalComments.value = (res as any).total || 0
}
// 新增留言
const showRemind = ref(false)
async function sendComment() {
  const data = {
    knowledgeId: id.value,
    content: commentInput.value,
  }
  if (!commentInput.value.trim())
    return
  const res = await addHealthKnowledgeCommentAPI(data)
  console.log(res.data)
  // 检查返回的data字段是否包含敏感词提醒
  if (res.data && typeof res.data === 'string' && res.data.includes('敏感词')) {
    showRemind.value = true
    setTimeout(() => {
      showRemind.value = false
    }, 5000)
  }
  commentInput.value = ''
  getCommentList(id.value)
}
// 图片预览
function previewCover() {
  if (!articleData.value.coverImage)
    return
  uni.previewImage({
    current: BASE_URL + articleData.value.coverImage,
    urls: [BASE_URL + articleData.value.coverImage],
  })
}
// 键盘高度变化处理
function onKeyboardShow(height: number) {
  inputBoxBottom.value = height
}

function onKeyboardHide() {
  inputBoxBottom.value = 0
}

onLoad((options: any) => {
  id.value = options.id
  getDetail(id.value)
  getCommentList(id.value)

  // 监听键盘高度变化
  uni.onKeyboardHeightChange((res) => {
    const height = res.height
    if (height > 0) {
      onKeyboardShow(height)
    }
    else {
      onKeyboardHide()
    }
  })
  // 检查登录状态
  checkLoginStatus()
})
// 用户登录状态
const isLoggedIn = ref(false)
// 检查登录状态
function checkLoginStatus() {
  isLoggedIn.value = checkUserLogin()
}
</script>

<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      知识详情
    </FgNavbar>

    <!-- 内容滚动区域 -->
    <scroll-view class="content-scroll" :scroll-y="true" :style="{ height: 'calc(100vh - 120rpx)' }">
      <view v-if="showRemind" class="evaluate-bar-remind">
        <image src="../../static/svg/remind.svg" mode="aspectFill" class="icon-remind" />
        检测到，您输入的信息存在敏感词，系统已屏蔽关键词！
      </view>

      <!-- 文章内容区域 -->
      <view class="article-container">
        <!-- 文章标题和元信息 -->
        <view class="article-header">
          <text class="article-title">
            {{ articleData.title }}
          </text>
          <view class="article-meta">
            <text class="author">
              发布人：{{ articleData.sendName }}
            </text>
            <text class="publish-time">
              {{ articleData.publishTime }}
            </text>
          </view>
        </view>
        <!-- 主图 -->
        <image
          class="article-cover"
          :src="BASE_URL + articleData.coverImage"
          mode="aspectFill"
          @click="previewCover"
        />
        <!-- 正文 -->
        <view class="article-content">
          <rich-text :nodes="articleData.articleComment" />
        </view>
      </view>
      <!-- 留言区 -->
      <view class="comment-section">
        <view class="comment-title">
          <text>留言</text>
          <text class="comment-count">
            ({{ totalComments }})
          </text>
        </view>
        <view v-for="item in comments" :key="item.id" class="comment-list">
          <view class="comment-item">
            <image class="comment-avatar" :src="item.patientAvatar ? BASE_URL + item.patientAvatar : '../../static/images/avatar.jpg'" mode="aspectFill" />
            <view class="comment-content">
              <view class="comment-content-top">
                {{ item.patientName }}
              </view>
              <view class="comment-content-bottom">
                {{ item.commentTime }}
              </view>
            </view>
          </view>
          <view class="comment-content-text">
            {{ item.content }}
          </view>
          <!-- 回复 -->
          <view v-if="item.reply" style="background-color: #F1F7FB;padding: 16rpx 24rpx; border-radius: 16rpx;margin-bottom: 24rpx;">
            <view class="comment-item">
              <image class="comment-avatar" :src="item.reply.replyAvatar ? BASE_URL + item.reply.replyAvatar : '../../static/images/avatar.jpg'" mode="aspectFill" />
              <view class="comment-content">
                <view class="comment-content-top">
                  {{ item.reply.replyName }}
                  <text class="self">
                    ({{ item.reply.eauthorReply ? '作者' : '非作者' }})
                  </text>
                </view>
                <view class="comment-content-bottom">
                  {{ item.reply.replyTime }}
                </view>
              </view>
            </view>
            <view class="comment-content-content">
              {{ item.reply.replyContent }}
            </view>
          </view>
        </view>
        <view v-if="comments.length === 0">
          <wd-status-tip
            :image-size="{ height: 120, width: 120 }"
            image="/static/images/empty.png"
            tip="暂无留言"
          />
        </view>
      </view>
      <!-- 底部占位，避免内容被输入框遮挡 -->
      <view class="bottom-placeholder" :class="{ 'with-input': isLoggedIn }" />
    </scroll-view>

    <!-- 输入框 -->
    <view v-if="isLoggedIn" class="input-bar" :style="{ bottom: `${inputBoxBottom}px` }">
      <textarea
        v-model="commentInput"
        class="input-bar-textarea"
        placeholder="请输入您的留言"
        :disabled="articleData.allowComment === 1 || articleData.delFlag === 1"
        :auto-height="true"
        :maxlength="500"
        :show-confirm-bar="false"
        :adjust-position="false"
        :cursor-spacing="10"
        :hold-keyboard="false"
        :disable-default-padding="true"
      />
      <text
        class="input-bar-button"
        :class="{ disabled: articleData.allowComment === 1 || articleData.delFlag === 1 }"
        @click="(articleData.allowComment !== 1 && articleData.delFlag !== 1) && sendComment()"
      >
        发送
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
$page-input-bar-height: 56px;

.page {
  background: #f5f5f5;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;

  .content-scroll {
    flex: 1;
    overflow: hidden;
  }
  .evaluate-bar-remind {
    position: fixed;
    left: 0;
    right: 0;
    z-index: 999;
    padding: 30rpx 32rpx;
    background: #f2f9ff;
    color: rgba(0, 0, 0, 0.45);
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;

    .icon-remind {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
  .article-container {
    background: #fff;
    margin: 12px 0 0 0;
    padding: 24rpx 32rpx 24rpx 32rpx;

    .article-header {
      .article-title {
        font-size: 40rpx;
        font-family: 'PingFang SC';
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 16rpx;
        line-height: 48rpx;
        display: block;
      }

      .article-meta {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
        line-height: 36rpx;
        margin-bottom: 16rpx;
      }
    }

    .article-cover {
      width: 100%;
      height: 386rpx;
      object-fit: cover;
      border-radius: 8rpx;
      margin-bottom: 16rpx;
      background: #eee;
    }

    .article-content {
      margin-bottom: 0;

      .content-text {
        overflow: hidden;
        color: rgba(0, 0, 0, 0.85);
        text-overflow: ellipsis;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: 0.28px;
      }
    }
  }

  .comment-section {
    background: #fff;
    margin: 16rpx 0 0 0;
    padding: 24rpx 32rpx 24rpx 32rpx;

    .comment-title {
      color: rgba(0, 0, 0, 0.85);
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 32rpx;
      margin-right: 8rpx;

      .comment-count {
        color: rgba(0, 0, 0, 0.45);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 32rpx;
      }
    }

    .comment-list {
      margin: 24rpx 0;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.15);

      .comment-item {
        display: flex;
        margin-bottom: 16rpx;

        .comment-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .comment-content {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .comment-content-top {
            color: rgba(0, 0, 0, 0.85);
            font-family: 'PingFang SC';
            font-size: 28rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 48rpx;
            .self {
              color: rgba(0, 0, 0, 0.45);
              text-align: center;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 16px; /* 133.333% */
            }
          }

          .comment-content-bottom {
            color: rgba(0, 0, 0, 0.25);
            margin-left: 16rpx;
            text-align: center;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 32rpx;
          }
        }
      }

      .comment-content-text {
        color: rgba(0, 0, 0, 0.85);
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 40rpx;
        margin-bottom: 24rpx;
      }
    }
  }

  .input-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    display: flex;
    align-items: flex-end;
    padding: 24rpx;
    border-top: 1px solid #f0f0f0;
    z-index: 100;
    box-sizing: border-box;

    /* iOS 安全区域适配 */
    /* #ifdef APP-PLUS || H5 */
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom, 0px));
    /* #endif */

    /* 微信小程序适配 */
    /* #ifdef MP-WEIXIN */
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom, 0px));
    /* #endif */

    /* Android 设备通常不需要额外的底部间距 */
    /* #ifdef APP-ANDROID */
    padding-bottom: 24rpx;
    /* #endif */

    .input-bar-textarea {
      border-radius: 36rpx;
      background: #f5f5f5;
      padding: 16rpx 24rpx;
      flex: 1;
      margin-right: 16rpx;
      min-height: 72rpx;
      max-height: 160rpx;
      font-size: 28rpx;
      line-height: 1.5;
      resize: none;
      border: none;
      outline: none;
      box-sizing: border-box;
      word-wrap: break-word;
      word-break: break-all;
    }

    .input-bar-textarea:disabled {
      background: #eee;
      color: #aaa;
    }

    .input-bar-button {
      border-radius: 36rpx;
      background: #007fee;
      color: #fff;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 1;
      padding: 24rpx 32rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      box-sizing: border-box;
    }
  }

  .input-bar-button.disabled {
    background: #cee8ff;
    color: #fff;
    pointer-events: none;
  }

  .bottom-placeholder {
    /* 默认小间距，未登录时使用 */
    height: 40rpx;

    /* 登录时为输入框预留空间 */
    &.with-input {
      /* iOS 和支持安全区域的设备 */
      /* #ifdef APP-PLUS || H5 || MP-WEIXIN */
      height: calc(120rpx + env(safe-area-inset-bottom, 0px) + 24rpx);
      /* #endif */

      /* Android 设备 */
      /* #ifdef APP-ANDROID */
      height: calc(120rpx + 24rpx);
      /* #endif */
    }
  }
}
</style>

<style scoped>
img,
rich-text img {
  max-width: 100%;
  height: auto;
  display: block;
}
</style>
