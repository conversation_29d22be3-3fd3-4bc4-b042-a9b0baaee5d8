<route lang="json5" type="page">
  {
    style: {
      navigationStyle: 'custom',
      navigationBarTitleText: '',
    },
  }
</route>

<script setup lang="ts">
import { ref } from 'vue'
import { getRecentHealthDataAPI } from '../../api/health-data'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'
import { useUserStore } from '../../store/user'

const userStore = useUserStore()
interface SubItem {
  label: string
  value: string | number
}

// 格式化数值为三位数显示
function formatValue(value: string | number): string {
  if (value === '-' || value === '' || value == null) {
    return '-'
  }
  const numValue = Number(value)
  if (isNaN(numValue)) {
    return String(value)
  }
  // 如果是数字，补齐为三位数显示
  return numValue.toString().padStart(3, ' ')
}

interface HealthDataItem {
  type: string
  icon: string
  bgIcon: string
  bgColor: string
  title: string
  unit: string
  main: string | number
  sub?: SubItem[]
}

interface ApiHealthData {
  patientHeightWeight?: { height?: number, weight?: number }
  patientBloodPressure?: { systolic?: number, diastolic?: number }
  patientBloodSugar?: { glucose?: number }
  patientBloodOxygen?: { arterialOxygen?: number, venousOxygen?: number }
  patientHeartRate?: { rate?: number }
  patientBodyFat?: { fatRate?: number }
}

const healthData = ref<HealthDataItem[]>([
  {
    type: 'heightWeight',
    icon: '../../static/svg/Health_Data.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodPressure-bj.png',
    bgColor: '#007fee',
    title: '身高体重',
    unit: 'cm/kg',
    main: '-/-',
  },
  {
    type: 'bloodPressure',
    icon: '../../static/svg/pressure.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodPressure-bj.png',
    bgColor: '#00ccd2',
    title: '血压',
    unit: 'mmHg',
    main: '',
    sub: [
      { label: '收缩压', value: '-' },
      { label: '舒张压', value: '-' },
    ],
  },
  {
    type: 'bloodSugar',
    icon: '../../static/svg/glucose.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodSugar-bj.png',
    bgColor: '#ff9500',
    title: '血糖',
    unit: 'mmol/L',
    main: '-',
  },
  {
    type: 'bloodOxygen',
    icon: '../../static/svg/oxygen.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodOxygen-bj.png',
    bgColor: '#a75cf7',
    title: '血氧',
    unit: '百分比',
    main: '',
    sub: [
      { label: '动脉血氧', value: '-' },
      { label: '静脉血氧', value: '-' },
    ],
  },
  {
    type: 'heartRate',
    icon: '../../static/svg/heartrate.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/heartRate-bj.png',
    bgColor: '#ff5656',
    title: '心率',
    unit: '次/分',
    main: '-',
  },
  {
    type: 'bodyFatRate',
    icon: '../../static/svg/bodyfatrate.svg',
    bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bodyfatrate-bj.png',
    bgColor: '#5cca3d',
    title: '体脂率',
    unit: '百分比',
    main: '-',
  },
])
// 获取数据
async function getData() {
  const res = await getRecentHealthDataAPI()
  const data = res.data as ApiHealthData
  // 假设 data.patientHeightWeight?.patientId 存在
  userStore.patientId = (data.patientHeightWeight as any)?.patientId || ''
  healthData.value = [
    {
      type: 'heightWeight',
      icon: '../../static/svg/Health_Data.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/heightWeight-bj.png',
      bgColor: '#007fee',
      title: '身高体重',
      unit: 'cm/kg',
      main: data.patientHeightWeight
        ? `${data.patientHeightWeight.height ?? '-'} / ${data.patientHeightWeight.weight ?? '-'}`
        : '- / -',
    },
    {
      type: 'bloodPressure',
      icon: '../../static/svg/pressure.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodPressure-bj.png',
      bgColor: '#00ccd2',
      title: '血压',
      unit: 'mmHg',
      main: '',
      sub: [
        { label: '收缩压', value: data.patientBloodPressure?.systolic ?? '-' },
        { label: '舒张压', value: data.patientBloodPressure?.diastolic ?? '-' },
      ],
    },
    {
      type: 'bloodSugar',
      icon: '../../static/svg/glucose.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodSugar-bj.png',
      bgColor: '#ff9500',
      title: '血糖',
      unit: 'mmol/L',
      main: data.patientBloodSugar?.glucose ?? '-',
    },
    {
      type: 'bloodOxygen',
      icon: '../../static/svg/oxygen.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bloodOxygen-bj.png',
      bgColor: '#a75cf7',
      title: '血氧',
      unit: '百分比',
      main: '',
      sub: [
        { label: '动脉血氧', value: data.patientBloodOxygen?.arterialOxygen ?? '-' },
        { label: '静脉血氧', value: data.patientBloodOxygen?.venousOxygen ?? '-' },
      ],
    },
    {
      type: 'heartRate',
      icon: '../../static/svg/heartrate.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/heartRate-bj.png',
      bgColor: '#ff5656',
      title: '心率',
      unit: '次/分',
      main: data.patientHeartRate?.rate != null ? String(data.patientHeartRate.rate) : '-',
    },
    {
      type: 'bodyFatRate',
      icon: '../../static/svg/bodyfatrate.svg',
      bgIcon: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/bodyfatrate-bj.png',
      bgColor: '#5cca3d',
      title: '体脂率',
      unit: '百分比',
      main: data.patientBodyFat?.fatRate ?? '-',
    },
  ]
}
// 点击跳转
function handleClick(item: any) {
  uni.navigateTo({
    url: `/pages-sub/health-data/detail?type=${item.type}`,
  })
}
onShow(() => {
  getData()
})
// 监听刷新事件
onMounted(() => {
  uni.$on && uni.$on('refreshHealthData', getData)
})
// 页面卸载时移除监听，防止内存泄漏
onUnmounted(() => {
  uni.$off && uni.$off('refreshHealthData', getData)
})
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="">
      健康数据
    </FgNavbar>
    <view class="card-grid">
      <view
        v-for="item in healthData" :key="item.title" class="card-grid-item" :style="{
          backgroundImage: item.bgIcon ? `url(${item.bgIcon})` : 'none',
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }" @click="handleClick(item)"
      >
        <view class="card-grid-item-icon" :style="{ backgroundColor: item.bgColor }">
          <image :src="item.icon" mode="scaleToFill" style="vertical-align: text-top;" />
        </view>
        <view class="card-grid-item-info">
          <view class="card-grid-item-info-title-unit">
            <view class="card-grid-item-info-title">
              {{ item.title }}
            </view>
            <view class="card-grid-item-info-unit">
              {{ item.unit }}
            </view>
          </view>
          <view class="card-grid-item-info-total">
            <template v-if="item.main && !item.sub">
              <text class="only-main main-value">
                {{ item.main }}
              </text>
            </template>
            <template v-if="item.main && item.sub">
              <text class="main-value with-sub">
                {{ item.main }}
              </text>
            </template>
            <template v-if="item.sub">
              <view v-for="(subItem, idx) in item.sub" :key="idx" class="card-grid-item-info-total-sub">
                <text class="sub-value">
                  {{ formatValue(subItem.value) }}
                </text>
                <text class="sub-label">
                  {{ subItem.label }}
                </text>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  min-height: 100vh;

  .card-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 24rpx;
    gap: 24rpx; // 12px = 24rpx

    .card-grid-item {
      width: 332rpx; // 166px = 332rpx
      height: 200rpx; // 100px = 200rpx
      padding: 24rpx;
      border-radius: 12rpx;
      display: flex;
      box-sizing: border-box;
      background-color: white;
      /* 卡片 */
      box-shadow: 0 4px 8px 0 rgba(0, 111, 255, 0.08);

      .card-grid-item-icon {
        width: 48rpx;
        height: 48rpx;
        padding: 8rpx;
        border-radius: 8rpx;
        box-sizing: border-box;
        flex-shrink: 0;
      }

      .card-grid-item-info {
        display: flex;
        flex-direction: column;
        margin-left: 16rpx;
        font-size: 24rpx;
        flex: 1;

        .card-grid-item-info-title-unit {
          display: flex;
          align-items: flex-end;
          margin-bottom: 12rpx;

          .card-grid-item-info-title {
            color: #333;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            font-size: 28rpx;
            margin-right: 6rpx;
          }

          .card-grid-item-info-unit {
            color: #999;
            font-family: 'PingFang SC';
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
          }
        }

        .card-grid-item-info-total {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;

          .main-value {
            color: #222;
            font-size: 40rpx;
            font-weight: 600;
            display: block;
            line-height: 1.2;
            margin-bottom: 4rpx;
            width: 100rpx;
            text-align: left;
            white-space: nowrap;
          }

          // 身高体重卡片特殊样式，不限制宽度
          .only-main {
            width: auto;
            text-align: left;
          }

          .only-main {
            margin-top: 0;
          }

          .with-sub {
            margin-top: 0;
          }
        }
      }
    }
  }
}

.card-grid-item-info-total-sub {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .sub-value {
    color: #222;
    font-size: 40rpx;
    font-weight: 600;
    margin-right: 8rpx;
    width: 100rpx;
    text-align: left;
    height: 18px;
  }

  .sub-label {
    color: #999;
    font-size: 24rpx;
    font-weight: 400;
  }
}

.empty {
  color: #bbb;
  text-align: center;
}
</style>
