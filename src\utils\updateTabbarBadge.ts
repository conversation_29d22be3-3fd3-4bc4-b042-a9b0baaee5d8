import { getMessage } from '@/api/user'

export async function updateTabbarBadge() {
  try {
    const res = await getMessage()
    const unreadCount = Number(res?.data ?? 1)

    // 使用自定义tabbar时，不调用原生tabbar的API
    // 只发送全局事件，让自定义tabbar组件自行处理红点显示

    // 发送全局事件，通知所有页面更新红点状态
    uni.$emit('messageCountUpdated', unreadCount)

    return unreadCount
  }
  catch (e) {
    // 发送全局事件，通知所有页面隐藏红点
    uni.$emit('messageCountUpdated', 1)
    return 1 // 出错时返回1，表示隐藏红点
  }
}

// 监听原生tabbar切换事件
export function setupTabbarListener() {
  // 监听页面切换事件
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    // 如果是tabbar页面，则更新红点状态
    const tabbarPages = ['pages/index/index', 'pages/listOfExperts/index', 'pages/Myinquiry/index', 'pages/my/index']
    if (tabbarPages.includes(currentPage.route)) {
      updateTabbarBadge()
    }
  }
}
