<route lang="json5" type="page">
  {
    style: {
      navigationStyle: 'custom',
      navigationBarTitleText: '',
    },
  }
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { privacyPolicyAPI } from '@/api/login'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'

const userAgreement = ref('')

onMounted(async () => {
  const res = await privacyPolicyAPI('2')
  userAgreement.value = res as any
})
</script>

<template>
  <view class="agreement-container">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      用户协议
    </FgNavbar>
    <scroll-view scroll-y class="agreement-scroll">
      <rich-text :nodes="userAgreement" />
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.agreement-container {
  background: #fff;
  padding: 10rpx 20rpx 60rpx 20rpx;
  box-sizing: border-box;
}
</style>
