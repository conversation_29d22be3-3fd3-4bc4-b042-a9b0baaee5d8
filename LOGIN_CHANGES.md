# 登录逻辑修改记录

## 修改目标
将小程序从强制登录模式改为可选登录模式，符合小程序审核要求。

## 修改的文件

### 1. 路由拦截器 (`src/interceptors/route.ts`)
**修改前**: 强制拦截所有非登录页面，未登录时跳转到登录页
**修改后**: 移除强制拦截，所有页面都可以正常访问

```typescript
// 修改前
const isNeedLogin = path !== loginPage // 判断是否需要登录
if (!isNeedLogin) { // 如果不需要登录，则直接返回 true
  return true
}
const hasLogin = isLogined() // 判断是否登录
if (hasLogin) { // 如果登录，则直接返回 true
  return true
}
const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}` // 跳转登录页
uni.navigateTo({ url: redirectRoute }) // 跳转登录页
return false

// 修改后
// 登录页面直接通过
const loginPage = '/pages/login/index'
if (path === loginPage) {
  return true
}
// 其他页面也直接通过，不强制登录
// 如果需要登录的功能，可以在具体页面内部判断
return true
```

### 2. 页面权限检查 (`src/hooks/usePageAuth.ts`)
**修改前**: 强制重定向到登录页
**修改后**: 移除强制重定向，改为页面内部处理

```typescript
// 修改前
const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(currentFullPath)}`
// 重定向到登录页
uni.redirectTo({ url: redirectRoute })

// 修改后
// 不强制重定向到登录页，而是让页面自己处理登录逻辑
// 页面可以通过显示登录提示或引导用户主动登录
console.log('当前页面需要登录，但采用可选登录模式')
```

### 3. 工具函数 (`src/utils/index.ts`)
**新增**: 添加登录检查工具函数

```typescript
/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function checkUserLogin(): boolean {
  return !!uni.getStorageSync('token')
}

/**
 * 引导用户登录（可选登录模式）
 * @param {string} message 提示信息
 * @param {string} loginUrl 登录页面路径
 */
export function guideToLogin(message = '请先登录后使用此功能', loginUrl = '/pages/login/index') {
  uni.showModal({
    title: '提示',
    content: message,
    confirmText: '去登录',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: loginUrl
        })
      }
    }
  })
}
```

### 4. 设置页面 (`src/pages-sub/setting/index.vue`)
**修改前**: 退出登录后强制跳转到登录页
**修改后**: 退出登录后返回首页

```typescript
// 修改前
uni.reLaunch({
  url: '/pages/login/index',
})

// 修改后
//   返回首页，不强制跳转登录页
uni.showToast({
  title: '已退出登录',
  icon: 'success',
  duration: 1500
})

// 延迟返回首页，让用户看到提示
setTimeout(() => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}, 1500)
```

### 5. HTTP请求拦截器 (`src/utils/request/alova.ts`)
**修改前**: 认证失败时强制跳转到登录页
**修改后**: 认证失败时清除token并提示用户

```typescript
// 修改前
// 切换到登录页
await uni.reLaunch({ url: '/pages/common/login/index' })

// 修改后
// 不强制跳转登录页，而是清除token并提示用户
console.log('认证失败，清除本地token')
uni.removeStorageSync('token')
uni.removeStorageSync('userInfo')
uni.removeStorageSync('user')

// 显示友好提示
uni.showToast({
  title: '登录已过期，请重新登录',
  icon: 'none',
  duration: 2000
})
```

### 6. HTTP工具函数 (`src/utils/http.ts`)
**修改前**: 401错误时强制跳转到登录页
**修改后**: 401错误时清除token并提示用户

```typescript
// 修改前
// 跳转到登录页（带上当前页面做重定向）
const loginPage = '/pages/login/index'
const currentPages = getCurrentPages()
const currentPage = currentPages[currentPages.length - 1] as any
if (`/${currentPage.route}` !== loginPage) {
  // 不是登录页才跳转
  const redirectUrl = currentPage
    ? `/${currentPage.route}${currentPage.options ? `?${Object.entries(currentPage.options).map(([k, v]) => `${k}=${v}`).join('&')}` : ''}`
    : '/pages/index/index'
  uni.navigateTo({ url: `/pages/login/index?redirect=${encodeURIComponent(redirectUrl)}` })
}

// 修改后
uni.removeStorageSync('token') // 清理token
uni.removeStorageSync('userInfo')
uni.removeStorageSync('user')

// 不强制跳转登录页，而是显示友好提示
uni.showToast({
  title: '登录已过期，请重新登录',
  icon: 'none',
  duration: 2000
})
```

### 7. 页面适配

#### 个人中心页面 (`src/pages/my/index.vue`)
- 添加登录状态检查
- 未登录时显示登录提示
- 已登录时显示个人信息

#### 问诊页面 (`src/pages/Myinquiry/index.vue`)
- 添加登录状态检查
- 未登录时显示登录提示
- 已登录时显示问诊记录

#### 健康数据页面 (`src/pages-sub/health-data/index.vue`)
- 添加登录状态检查
- 未登录时显示登录提示
- 已登录时显示健康数据

## 登录模式特点

### ✅ 符合小程序审核要求
1. **不强制登录**: 用户可以正常浏览小程序
2. **功能体验**: 提供基础功能让用户体验
3. **友好提示**: 使用弹窗而不是强制跳转
4. **功能区分**: 明确区分需要登录和不需要登录的功能

### ✅ 用户体验优化
1. **可选登录**: 只有在使用需要登录的功能时才会提示
2. **友好提示**: 使用弹窗提示用户登录，而不是强制跳转
3. **状态管理**: 页面内部管理登录状态，提供不同的UI展示
4. **错误处理**: 认证失败时清除token并提示用户，而不是强制跳转

### ✅ 功能区分
- **需要登录的功能**: 个人中心、问诊、健康数据、病史上传
- **不需要登录的功能**: 首页浏览、专家列表、健康知识阅读

## 测试要点

1. **首页访问**: 未登录用户可以正常访问首页
2. **功能浏览**: 可以浏览专家列表、健康知识等基础功能
3. **登录提示**: 访问需要登录的功能时显示友好提示
4. **退出登录**: 退出后返回首页，不强制跳转登录页
5. **认证失败**: 401错误时清除token并提示，不强制跳转

## 注意事项

1. 所有强制跳转到登录页的逻辑都已移除
2. 使用 `checkUserLogin()` 和 `guideToLogin()` 进行登录检查
3. 页面内部根据登录状态显示不同的UI
4. 认证失败时清除本地token并显示友好提示 