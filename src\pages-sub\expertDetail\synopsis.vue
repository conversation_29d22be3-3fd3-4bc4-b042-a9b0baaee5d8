<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { checkUserLogin } from '@/utils'

const introduction = ref('')
const expertId = ref()
// 移除scrollViewHeight相关

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync()
  const windowHeight = systemInfo.windowHeight // 屏幕高度 px
  const statusBarHeight = systemInfo.statusBarHeight || 0 // 状态栏高度 px
  const navBarHeight = 44 // 自定义导航栏高度 px
  const bottomBtnHeight = 80 // 底部按钮高度 px
  // 动态计算内容区高度
  // scrollViewHeight.value = `${windowHeight - statusBarHeight - navBarHeight - bottomBtnHeight}px`
})

onLoad((options) => {
  expertId.value = options.id
  introduction.value = options.introduction || ''
  checkLoginStatus()
})
// 立即咨询
function handleConsult() {
  console.log('立即咨询')
  uni.navigateTo({
    url: `/pages-sub/questions/index?id=${expertId.value}`,
  })
}
// 用户登录状态
const isLoggedIn = ref(false)
// 检查登录状态
function checkLoginStatus() {
  isLoggedIn.value = checkUserLogin()
}
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      专家简介
    </FgNavbar>
    <scroll-view
      class="synopsis-scroll"
      scroll-y
    >
      <view class="synopsis-content">
        <view class="synopsis-content-item">
          <text>
            {{ introduction }}
          </text>
        </view>
      </view>
    </scroll-view>
    <view v-if="isLoggedIn" class="bottom-btn-wrap" @click="handleConsult">
      <button class="consult-btn">
        立即咨询
      </button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .synopsis-scroll {
    flex: 1; // 让scroll-view自适应高度
    margin-bottom: 160rpx;

    .synopsis-content {
      .synopsis-content-item {
        padding: 24rpx 32rpx;
        background-color: #fff;
        font-size: 28rpx;
        color: #333;
        line-height: 1.8;
      }
    }
  }

  .bottom-btn-wrap {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24rpx 0 48rpx 0;
    background: #fff;
    border-bottom-left-radius: 32rpx;
    border-bottom-right-radius: 32rpx;
    box-shadow: 0 -8rpx 24rpx 0 rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;
    z-index: 10;

    .consult-btn {
      width: 90vw;
      height: 88rpx;
      background: #1890ff;
      color: #fff;
      border-radius: 44rpx;
      font-size: 32rpx;
      text-align: center;
      line-height: 88rpx;
      border: none;
    }
  }
}
</style>
