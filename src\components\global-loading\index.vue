<script setup lang="ts">
import { useLoadingStore } from '@/store/loading'

const loadingStore = useLoadingStore()
</script>

<template>
  <view v-if="loadingStore.isLoading" class="global-loading">
    <view class="loading-container">
      <view class="loading-spinner">
        <view class="spinner-dot" />
        <view class="spinner-dot" />
        <view class="spinner-dot" />
      </view>
      <text class="loading-text">
        {{ loadingStore.loadingText }}
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      display: flex;
      gap: 8rpx;
      margin-bottom: 32rpx;

      .spinner-dot {
        width: 16rpx;
        height: 16rpx;
        background: #007fee;
        border-radius: 50%;
        animation: loading-bounce 1.4s ease-in-out infinite both;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }
  }
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
