<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: ' ',
      },
    }
</route>

<script lang="ts" setup>
import { logoutAPI } from '@/api/login'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'
// 退出登录逻辑
async function logout() {
  //   询问是否退出登录
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const res = await logoutAPI()
          console.log(res)
        }
        catch (error) {
          console.error('退出登录失败:', error)
        }

        //   清除缓存
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('user')

        //   返回首页，不强制跳转登录页
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
          duration: 1500,
        })

        // 延迟返回首页，让用户看到提示
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    },
  })
}
// 关注公众号逻辑
function follow() {
  uni.showToast({ title: '请在公众号内搜索并关注', icon: 'none' })
}
</script>

<template>
  <FgNavbar :left-arrow="true" left-text="" :fixed="true">
    设置
  </FgNavbar>
  <view class="setting-bg min-h-screen pt-[64px]">
    <view class="setting-card shadow-card mx-[20px] mt-[36px] rounded-[24px] p-[32px]">
      <view class="flex flex-col items-center space-y-[36px]">
        <!-- <button class="setting-btn-follow" @click="follow">
          <text class="iconfont icon-wechat mr-[8px]" /> 关注公众号
        </button> -->
        <button class="setting-btn-logout" @click="logout">
          退出登录
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.setting-bg {
  background: #f6f7fb;
  min-height: 100vh;
}
.setting-card {
  background: #fff;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.04);
  border-radius: 48rpx;
}
.setting-btn-follow {
  width: 100%;
  height: 96rpx;
  background: #fff;
  color: #222;
  font-size: 32rpx;
  font-weight: 500;
  border: 3rpx solid #e5e6eb;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s;
  letter-spacing: 2rpx;
}
.setting-btn-follow:active {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.setting-btn-logout {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient(90deg, #ff7a6a 0%, #ff4d4f 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  border-radius: 24rpx;
  transition: opacity 0.2s;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.08);
}
.setting-btn-logout:active {
  opacity: 0.85;
}
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 36rpx;
}
.shadow-card {
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.04);
}
</style>
