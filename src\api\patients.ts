import { http } from '@/utils/http'

// 获取就诊人信息列表
export function getPatientListAPI() {
  return http.get('/h5/disease/patient/list')
}
// 新增就诊人信息
export function addPatientAPI(data: any) {
  return http.post('/h5/disease/patient', data)
}
// 获取配置信息
export function getConfigAPI() {
  return http.get('/h5/disease/patient/getConfig')
}
// 选中就诊人
export function get_patient_selected(id: number) {
  return http.get(`/h5/disease/patient/selected/${id}`)
}

// 获取就诊人详细信息
export function get_patient_info(id: number) {
  return http.get(`/h5/disease/patient/${id}`)
}
// 修改就诊人信息
export function put_patient_info(data) {
  return http.put(`/h5/disease/patient`, data)
}
// 删除就诊人
export function del_patientAPI(id) {
  return http.delete(`/h5/disease/patient/${id}`)
}
