import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoadingStore = defineStore('loading', () => {
  const isLoading = ref(false)
  const loadingText = ref('加载中...')

  const showLoading = () => {
    loadingText.value = '加载中...'
    isLoading.value = true
  }

  const hideLoading = () => {
    isLoading.value = false
  }

  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
  ): Promise<T> => {
    try {
      showLoading()
      const result = await asyncFn()
      return result
    }
    finally {
      hideLoading()
    }
  }

  return {
    isLoading,
    loadingText,
    showLoading,
    hideLoading,
    withLoading,
  }
})
