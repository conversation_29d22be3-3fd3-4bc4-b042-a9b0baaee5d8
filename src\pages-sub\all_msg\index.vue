<route lang="json5" type="page">
    {
      layout: 'tabbar',
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { get_messageNotification_list } from '@/api/all_msg'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'

interface data_type {
  title: string
  content: string
  publishTime: string
  status: number
  messageType: 1 | 2
  expertId: number
  knowledgeId: number
}

const data_arr = ref<data_type[]>()
onShow(() => {
  get_messageNotification_list().then((res: any) => {
    console.log(res)
    data_arr.value = res.rows
  })
})
function to_info(type: 1 | 2, id: number) {
  if (type === 1) {
    uni.navigateTo({
      url: `/pages-sub/questions/index?id=${id}`,
    })
  }
  else if (type === 2) {
    uni.navigateTo({
      url: `/pages-sub/health-knowledge/detail?id=${id}`,
    })
  }
}
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      全部消息
    </FgNavbar>
    <view>
      <view v-if="data_arr && data_arr.length > 0">
        <view
          v-for="(item, index) in data_arr" :key="index" class="flex items-center border-b-[#F2F2F2] py-[16px]"
          @click="to_info(item.messageType, item.messageType === 1 ? item.expertId : item.knowledgeId)"
        >
          <view
            class="pos-relative my-[6px] h-[40px] w-[40px] flex items-center justify-center rounded-full bg-[#E0F1FF]"
          >
            <img src="../../static/svg/xiaoxi.svg" class="h-[24px] w-[24px]" alt="">
            <div v-if="item.status === 0" class="pos-absolute right-[10%] top-[10%] h-[6px] w-[6px] rounded-full bg-red" />
          </view>
          <view class="ml-[8px] max-w-[85%]">
            <view class="mb-[12px] flex justify-between">
              <view class="font-500">
                <!-- {{ item.title }} -->
                系统消息
              </view>
              <view class="text-[12px] text-[#999]">
                {{ item.publishTime }}
              </view>
            </view>
            <view class="text-ellipsis text-[14px] text-[#666]">
              {{ item.content }}
            </view>
          </view>
        </view>
      </view>
      <view v-else class="empty-status-center">
        <wd-status-tip
          :image-size="{ height: 120, width: 120 }"
          image="/static/images/empty.png"
          tip="暂无消息"
        />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #fff;
  padding: 0 16px;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* 超出部分显示省略号 */
  max-width: 100%;
  /* 确保容器不溢出 */
}

.empty-status-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh; // 你可以根据实际页面高度调整
}
</style>
