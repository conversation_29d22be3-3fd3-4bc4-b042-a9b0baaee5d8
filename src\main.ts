import { VueQueryPlugin } from '@tanstack/vue-query'
import { createSSRApp } from 'vue'
import share from '@/utils/share'
import App from './App.vue'

import { prototypeInterceptor, requestInterceptor, routeInterceptor } from './interceptors'
import store from './store'
import '@/style/index.scss'
import 'virtual:uno.css'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  app.use(VueQueryPlugin)
  app.mixin(share)
  return {
    app,
  }
}
