/**
 * tabbar 选择的策略，更详细的介绍见 tabbar.md 文件
 * 0: 'NO_TABBAR' `无 tabbar`
 * 1: 'NATIVE_TABBAR'  `完全原生 tabbar`
 * 2: 'CUSTOM_TABBAR_WITH_CACHE' `有缓存自定义 tabbar`
 * 3: 'CUSTOM_TABBAR_WITHOUT_CACHE' `无缓存自定义 tabbar`
 *
 * 温馨提示：本文件的任何代码更改了之后，都需要重新运行，否则 pages.json 不会更新导致错误
 */
export const TABBAR_MAP = {
  NO_TABBAR: 0,
  NATIVE_TABBAR: 1,
  CUSTOM_TABBAR_WITH_CACHE: 2,
  CUSTOM_TABBAR_WITHOUT_CACHE: 3,
}
// TODO：通过这里切换使用tabbar的策略
export const selectedTabbarStrategy = TABBAR_MAP.NATIVE_TABBAR

// selectedTabbarStrategy==NATIVE_TABBAR(1) 时，需要填 iconPath 和 selectedIconPath
// selectedTabbarStrategy==CUSTOM_TABBAR(2,3) 时，需要填 icon 和 iconType
// selectedTabbarStrategy==NO_TABBAR(0) 时，tabbarList 不生效
export const tabbarList = [
  {
    iconPath: 'static/tabbar/home.png',
    selectedIconPath: 'static/tabbar/homeHL.png',
    pagePath: 'pages/index/index',
    text: '首页',
  },
  {
    iconPath: 'static/tabbar/list.png',
    selectedIconPath: 'static/tabbar/listHL.png',
    pagePath: 'pages/listOfExperts/index',
    text: '专家列表',
  },
  {
    iconPath: 'static/tabbar/consult.png',
    selectedIconPath: 'static/tabbar/consultHL.png',
    pagePath: 'pages/Myinquiry/index',
    text: '我的问诊',
  },
  {
    iconPath: 'static/tabbar/my.png',
    selectedIconPath: 'static/tabbar/myHL.png',
    pagePath: 'pages/my/index',
    text: '个人中心',
  },
]

// NATIVE_TABBAR(1) 和 CUSTOM_TABBAR_WITH_CACHE(2) 时，需要tabbar缓存
export const cacheTabbarEnable = selectedTabbarStrategy === TABBAR_MAP.NATIVE_TABBAR
  || selectedTabbarStrategy === TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE

const _tabbar = {
  custom: true,
  color: '#4F6272', // 未选中颜色
  selectedColor: '#007FEE', // 选中颜色
  backgroundColor: '#ffffff', // 背景颜色
  borderStyle: 'black', // 边框颜色
  height: '50px', // 高度
  fontSize: '30rpx', // 字体大小
  iconWidth: '48rpx', // 图标宽度
  spacing: '8rpx', // 间距
  list: tabbarList, // 列表
}

// 0和1 需要显示底部的tabbar的各种配置，以利用缓存
export const tabBar = cacheTabbarEnable ? _tabbar : undefined
