<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  getHealthKnowledgeListAPI,
  getHealthKnowledgeTabsAPI,
  isFollowWechatAPI,
} from '@/api/index'
import { getAuditAPI } from '@/api/login'
import { getPatientListAPI } from '@/api/patients'
import CustomTabbar from '@/components/custom-tabbar/index.vue'
import { useLoadingStore, useUserStore } from '@/store'
import { checkUserLogin, guideToLogin } from '@/utils'
import { updateTabbarBadge } from '@/utils/updateTabbarBadge'
import DetailsComponent from '../details.vue'

// 页面loading
const loadingStore = useLoadingStore()

// 获取本地存储的audit，使用响应式ref
const tabbarType = ref(Number(uni.getStorageSync('audit')))
console.log('从本地存储获取 audit:', tabbarType.value)

const img_url = import.meta.env.VITE_IMG_URL
//   状态栏高度
const statusBarHeight = ref(0)

// 健康知识tab栏
const tabs = ref([])
const activeTab = ref(0)
// 当前选中的就诊人
const currentPatient = ref({
  name: '', // 姓名
  relationshipName: '', // 关系
})
// 更多
function handleMore() {
  uni.navigateTo({
    url: '/pages-sub/health-knowledge/index',
  })
}
// 检查登录状态并显示弹窗
function checkLoginAndShowDialog(message = '请登录后使用此功能') {
  if (!checkUserLogin()) {
    guideToLogin(message)
    return false
  }
  return true
}

// 智能匹配
function handleSmartMatch() {
  // if (!checkLoginAndShowDialog('请登录后使用智能匹配功能'))
  //   return
  uni.navigateTo({
    url: '/pages-sub/expertDetail/SmartMatch',
  })
}
// 健康数据
function handleHealthData() {
  if (!checkLoginAndShowDialog('请登录后查看健康数据'))
    return
  uni.navigateTo({
    url: '/pages-sub/health-data/index',
  })
}
// 病史上传
function handleExpertRecord() {
  if (!checkLoginAndShowDialog('请登录后上传病史'))
    return
  uni.navigateTo({
    url: '/pages-sub/medical_history/upload',
  })
}
// 获取健康知识列表
const pages = ref({
  pageNum: 1,
  pageSize: 10,
  articleType: '',
})
const healthKnowledgeList = ref([])
// 获取健康知识列表
async function getHealthKnowledgeList() {
  await loadingStore.withLoading(async () => {
    const res: any = await getHealthKnowledgeListAPI(pages.value)
    healthKnowledgeList.value = Array.isArray(res.rows) ? res.rows : []
  })
}
// 切换tab
function onTabChange(idx) {
  activeTab.value = idx
  pages.value.articleType = tabs.value[idx].dictValue
  getHealthKnowledgeList()
}
// 健康知识详情
function handleHealthKnowledgeDetail(item) {
  uni.navigateTo({
    url: `/pages-sub/health-knowledge/detail?id=${item.id}`,
  })
  console.log('健康知识详情', item.id)
}
// 获取就诊人信息
async function getPatientInfo() {
  await loadingStore.withLoading(async () => {
    const res = await getPatientListAPI()
    // selected 为1的就诊人
    console.log(res.data, 1111)
    console.log(Array.isArray(res.data), 2222)

    if (Array.isArray(res.data)) {
      currentPatient.value = res.data.find((item: any) => item.selected === 1) || {}
    }
    else {
      currentPatient.value = {
        name: '', // 姓名
        relationshipName: '', // 关系
      }
      console.log(333)
    }
  })
}
// 切换就诊人
function handleSwitchPatient() {
  uni.navigateTo({
    url: '/pages-sub/switch_patients/index',
  })
}

// 从pinia中获取微信登录code
const userStore = useUserStore()
const code = userStore.code
// 获取是否关注公众号
const isFollowWechat = ref(false)
async function getIsFollowWechat() {
  const res = await isFollowWechatAPI({ code })
  if (res.msg === null) {
    isFollowWechat.value = true
    // 存储到本地
    uni.setStorageSync('isFollowWechat', true)
  }
  else {
    isFollowWechat.value = false
    // 存储到本地
    uni.setStorageSync('isFollowWechat', false)
  }
}
onShow(async () => {
  await loadingStore.withLoading(async () => {
    // 重新获取最新的 audit 值，确保切换就诊人后能正确更新
    try {
      const auditRes = await getAuditAPI()
      const newAudit = Number(auditRes.msg)
      if (tabbarType.value !== newAudit) {
        tabbarType.value = newAudit
        uni.setStorageSync('audit', auditRes.msg)
        console.log('首页更新 audit 值:', newAudit)
      }
    }
    catch (error) {
      console.error('获取 audit 失败:', error)
      // 如果获取失败，使用本地存储的值
      const currentAudit = Number(uni.getStorageSync('audit'))
      if (tabbarType.value !== currentAudit) {
        tabbarType.value = currentAudit
        console.log('使用本地存储的 audit 值:', currentAudit)
      }
    }

    // 获取状态栏高度
    const info = uni.getSystemInfoSync()
    statusBarHeight.value = info.statusBarHeight
    // 获取tabs的字典
    const res = await getHealthKnowledgeTabsAPI()
    if (Array.isArray(res.data) && res.data.length > 0) {
      tabs.value = res.data
      pages.value.articleType = tabs.value[0].dictValue
    }
    else {
      tabs.value = []
    }
    await getHealthKnowledgeList()
    await getPatientInfo()
    console.log(currentPatient)
    // getIsFollowWechat()
    // 每次显示页面时都更新红点状态
    updateTabbarBadge()
  })
})
</script>

<template>
  <DetailsComponent v-if="tabbarType === 0" />
  <view v-if="tabbarType === 1" class="page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: `${statusBarHeight}px` }">
      <view class="navbar-title">
        <image class="avatar-title" src="@/static/images/title.png" mode="widthFix" />
      </view>
    </view>
    <!-- <view v-if="!isFollowWechat" class="mt-[10px]">
      <wd-notice-bar text="关注公众号，接收通知消息！" type="info" :scrollable="false" prefix="check-outline" />
    </view> -->
    <!-- 个人信息栏 -->
    <view v-if="currentPatient.name" class="user-bar">
      <view class="user-info">
        <image class="avatar" src="@/static/svg/UserCard.svg" />
        <text class="username">
          {{ currentPatient.name }}
        </text>
        <text
          v-if="currentPatient.relationshipName" :class="{
            my_button: currentPatient.relationshipName === '本人',
            family_button: currentPatient.relationshipName === '家人',
            relative_button: currentPatient.relationshipName !== '本人' && currentPatient.relationshipName !== '家人',
          }"
        >
          {{ currentPatient.relationshipName }}
        </text>
      </view>
      <view class="switch-patient" @click="handleSwitchPatient">
        <text>切换就诊人</text>
        <image class="switch-patient-icon" src="@/static/svg/cut.svg" />
      </view>
    </view>
    <view v-else class="user-bar" />

    <!-- 功能卡片区 -->
    <view class="card-area">
      <view class="card-main" @click="handleSmartMatch">
        <view class="card-title">
          智能匹配
        </view>
        <view class="card-desc">
          精准推荐医生
        </view>
      </view>
      <view class="card-group">
        <view class="card-orange" @click="handleHealthData">
          <view class="card-title">
            健康数据
          </view>
          <view class="card-desc">
            健康数据全监测
          </view>
        </view>
        <view class="card-blue" @click="handleExpertRecord">
          <view class="card-title">
            病史上传
          </view>
          <view class="card-desc">
            全生命周期档案
          </view>
        </view>
      </view>
    </view>
    <!-- 健康知识  -->
    <view class="health-knowledge-flex">
      <view class="health-knowledge-title">
        <view class="health-knowledge-title-text">
          健康知识
        </view>
        <view class="health-knowledge-title-more" @click="handleMore">
          <text class="more">
            更多
          </text>
          <image src="@/static/svg/arrows.svg" class="arrows" />
        </view>
      </view>
      <!-- 新增tab栏 -->
      <view class="health-knowledge-tabs">
        <view
          v-for="(tab, idx) in tabs"
          :key="tab.dictValue"
          class="tab-item"
          :class="[{ active: idx === activeTab }]"
          @click="onTabChange(idx)"
        >
          {{ tab.dictLabel }}
        </view>
      </view>
      <!-- 健康知识列表滚动区 -->
      <scroll-view class="health-knowledge-list" :scroll-y="true">
        <view
          v-for="item in healthKnowledgeList"
          :key="item.id"
          class="health-knowledge-item"
          @click="handleHealthKnowledgeDetail(item)"
        >
          <image
            :src="item.coverImage ? (img_url + item.coverImage) : '/static/images/avatar.jpg'"
            class="health-knowledge-item-img"
          />
          <view class="health-knowledge-item-content">
            <view class="health-knowledge-item-title">
              {{ item.title }}
            </view>
            <view class="health-knowledge-item-meta">
              发布人员：{{ item.sendName }}
            </view>
            <view class="health-knowledge-item-meta">
              发布时间：{{ item.publishTime }}
            </view>
          </view>
        </view>
        <view v-if="healthKnowledgeList.length === 0" class="empty-tip">
          <wd-status-tip
            :image-size="{ height: 120, width: 120 }"
            image="/static/images/empty.png"
            tip="暂无健康知识"
          />
        </view>
      </scroll-view>
    </view>
    <!-- 自定义tabbar -->
    <CustomTabbar />
  </view>
</template>

<style scoped lang="scss">
.page {
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  padding: 0 32rpx 120rpx 32rpx; // 底部增加120rpx为tabbar预留空间
  background: linear-gradient(168deg, #fffbfd -1.11%, #d9f0ff 14.9%, #f0f9ff 32.63%, #f1f1f1 36.32%);

  .custom-navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .navbar-title {
      width: 310rpx;
      height: 52rpx;
      margin-top: 20rpx;
      .avatar-title {
        width: 100%;
        height: 100%;
      }
    }
  }

  .user-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 32rpx 0 0 0;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx 16rpx 0 0;
    border-bottom: 1px solid #f0f1f2;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
      }

      .username {
        font-size: 32rpx;
        font-family: 'PingFang SC';
        color: #007fee;
        margin-right: 8rpx;
      }

      // .tag {
      //   background: #fcf1e4;
      //   color: #cf9358;
      //   border-radius: 16rpx;
      //   padding: 0rpx 24rpx;
      //   font-size: 24rpx;
      //   height: 40rpx;
      //   line-height: 40rpx;
      // }
      .my_button {
        display: flex;
        padding: 0px 24rpx;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #cf9358; // 本人
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        border-radius: 40rpx;
        background: #fcf1e4; // 本人
      }
      .family_button {
        display: flex;
        padding: 0px 24rpx;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #273554; // 家人
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        border-radius: 40rpx;
        background: #e9ebee; // 家人
      }
      .relative_button {
        display: flex;
        padding: 0px 24rpx;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #273554; // 亲戚/其他：灰色
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        border-radius: 40rpx;
        background: #e9ebee; // 亲戚/其他：浅灰
      }
    }

    .switch-patient {
      color: #007fee;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      font-weight: 500;

      .switch-patient-icon {
        width: 40rpx;
        height: 40rpx;
        margin-left: 8rpx;
      }
    }
  }
  .card-area {
    display: flex;
    background: #fff;
    border-radius: 0 0 16rpx 16rpx;
    padding: 24rpx;
    .card-main {
      width: 308rpx;
      height: 320rpx;
      border-radius: 16rpx;
      background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/smart-bj.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .card-title {
        margin: 24rpx 0 10rpx 24rpx;
        color: #007fee;
        font-family: 'PingFang SC';
        font-size: 40rpx;
        font-weight: 700;
      }
      .card-desc {
        margin-left: 24rpx;
        color: #4388e1;
        font-family: 'PingFang SC';
        font-weight: 400;
        font-size: 28rpx;
      }
    }
    .card-group {
      margin-left: 24rpx;
      .card-orange {
        width: 308rpx;
        height: 148rpx;
        border-radius: 16rpx;
        background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/health-bj.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-bottom: 24rpx;
        padding: 24rpx 0 0 24rpx;
        box-sizing: border-box;
        .card-title {
          color: #b85c00;
          font-family: 'PingFang SC';
          font-size: 32rpx;
          font-weight: 700;
          line-height: 48rpx;
        }
        .card-desc {
          color: #cf9358;
          font-family: 'PingFang SC';
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 48rpx;
        }
      }
      .card-blue {
        width: 308rpx;
        height: 148rpx;
        border-radius: 16rpx;
        background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/history-bj.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 24rpx 0 0 24rpx;
        box-sizing: border-box;
        .card-title {
          color: #002fb5;
          font-family: 'PingFang SC';
          font-size: 32rpx;
          font-weight: 700;
          line-height: 48rpx;
        }
        .card-desc {
          color: #6985d4;
          font-family: 'PingFang SC';
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 48rpx;
        }
      }
    }
  }

  .health-knowledge-flex {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    margin-top: 24rpx;
    padding: 24rpx 24rpx 0 24rpx;
    border-radius: 16rpx;
    background: linear-gradient(180deg, #f6faff 2.84%, #fff 18.21%);
    box-shadow: 0px 8rpx 8rpx 0px rgba(0, 111, 255, 0.1);
    margin-bottom: 60rpx;
  }

  .health-knowledge-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .health-knowledge-title-text {
      color: rgba(0, 0, 0, 0.85);
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 700;
    }

    .health-knowledge-title-more {
      display: flex;
      align-items: center;

      .more {
        color: #999;
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 48rpx;
        /* 200% */
      }

      .arrows {
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
      }
    }
  }

  .health-knowledge-tabs {
    display: flex;
    margin-top: 24rpx;
    margin-bottom: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tab-item {
      color: #000;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 500;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;

      &.active {
        color: #fff;
        background: #1890ff;
        border-color: #1890ff;
        z-index: 1;
      }
    }
  }

  .health-knowledge-list {
    flex: 1;
    min-height: 0;
    overflow: auto;
  }

  .health-knowledge-item {
    display: flex;
    align-items: flex-start;
    padding: 24rpx 0;
    border-bottom: 1px solid #f2f2f2;
    background: #fff;
    .health-knowledge-item-img {
      width: 196rpx;
      height: 144rpx;
      border-radius: 8rpx;
      object-fit: cover;
      flex-shrink: 0;
      margin-right: 24rpx;
    }
    .health-knowledge-item-content {
      .health-knowledge-item-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #222;
        margin-bottom: 12rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 限制为2行 */
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .health-knowledge-item-meta {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 4rpx;
        margin-top: 8rpx;
      }
    }
  }

  .empty-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600rpx;
    width: 100%;
  }
}
</style>
