<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { getHealthKnowledgeListAPI, getHealthKnowledgeTabsAPI } from '@/api/index'

const img_url = import.meta.env.VITE_IMG_URL
const tabs = ref([])
const activeTab = ref('')
const list = ref([])
const pages = ref({
  pageNum: 1,
  pageSize: 10,
  articleType: '',
})
const hasMore = ref(true) // 是否还有更多
const loading = ref(false) // 加载中
const scrollTop = ref(0)
const total = ref(0) // 新增 total 变量
type LoadMoreState = 'loading' | 'finished' | 'error'
const loadState = ref<LoadMoreState>('loading')

// 获取tabs
async function getTabs() {
  const res = await getHealthKnowledgeTabsAPI()
  if (Array.isArray(res.data)) {
    tabs.value = res.data.map((item: any) => ({
      label: item.dictLabel,
      value: item.dictValue,
    }))
    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].value
      pages.value.articleType = tabs.value[0].value
      getHealthKnowledgeList()
    }
  }
  else {
    tabs.value = []
  }
}
// 获取健康知识列表
async function getHealthKnowledgeList(isLoadMore = false) {
  if (loading.value)
    return
  loading.value = true
  const res: any = await getHealthKnowledgeListAPI(pages.value)
  const rows = Array.isArray(res.rows) ? res.rows : []
  total.value = res.total || 0 // 赋值 total
  if (isLoadMore) {
    list.value = list.value.concat(rows)
  }
  else {
    list.value = rows
  }
  // 判断空状态
  if (list.value.length === 0) {
    loadState.value = 'finished'
    hasMore.value = false
  }
  else if (list.value.length < total.value) {
    loadState.value = 'loading'
    hasMore.value = true
  }
  else {
    loadState.value = 'finished'
    hasMore.value = false
  }
  loading.value = false
}
// 加载更多
function loadMore() {
  if (!hasMore.value || loading.value)
    return
  pages.value.pageNum += 1
  getHealthKnowledgeList(true)
}
// tab点击事件
function onTabClick(val: string) {
  activeTab.value = val
  pages.value.articleType = val
  pages.value.pageNum = 1
  // 先赋一个大值，再归零，确保触发滚动
  scrollTop.value = 1
  nextTick(() => {
    scrollTop.value = 0
  })
  getHealthKnowledgeList()
}
// 详情
function handleClick(item: any) {
  uni.navigateTo({
    url: `/pages-sub/health-knowledge/detail?id=${item.id}`,
  })
}
onMounted(() => {
  getTabs()
})
</script>

<template>
  <view class="page">
    <!-- 固定导航栏 -->
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      健康知识
    </FgNavbar>

    <!-- 标签栏 -->
    <view class="tab-bar">
      <view
        v-for="tab in tabs"
        :key="tab.value"
        class="tab-item"
        :class="[{ active: activeTab === tab.value }]"
        @click="onTabClick(tab.value)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 滚动内容区 -->
    <scroll-view class="scroll-content" scroll-y :scroll-top="scrollTop" @scrolltolower="loadMore">
      <view class="knowledge-list">
        <view v-if="!loading && list.length === 0" class="empty">
          <wd-status-tip
            :image-size="{ height: 120, width: 120 }"
            image="/static/images/empty.png"
            tip="暂无健康知识"
          />
        </view>
        <view v-else>
          <view v-for="(item, idx) in list" :key="idx" class="knowledge-item" @click="handleClick(item)">
            <image class="item-img" :src="item.coverImage ? (img_url + item.coverImage) : '/static/images/avatar.jpg'" mode="aspectFill" />
            <view class="item-content">
              <view class="item-title">
                {{ item.title }}
              </view>
              <view class="item-meta">
                <text>发布人：{{ item.sendName }}</text>
                <text class="item-date">
                  发布时间：{{ item.publishTime }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <wd-loadmore
        v-if="list.length > 0"
        custom-class="loadmore"
        :state="loadState"
        loading-text="加载中..."
        finished-text="没有更多了"
        error-text="加载失败，点击重试"
        @reload="loadMore"
      />
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
}
.tab-bar {
  display: flex;
  margin-top: 32rpx;
  padding: 0 32rpx;
  margin-bottom: 16rpx;
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 8rpx 16rpx;
    font-family: 'PingFang SC';
    font-size: 28rpx;
    color: #000;
    border-radius: 8rpx;
  }
  .tab-item.active {
    color: #fff;
    background: #007fee;
    font-weight: bold;
    font-family: 'PingFang SC';
    font-size: 28rpx;
  }
}
.scroll-content {
  flex: 1;
  overflow-y: auto;
  height: 0; // 让flex:1生效
  padding-bottom: 48rpx; // 新增底部内边距
}
.knowledge-list {
  padding: 0 32rpx;
  .knowledge-item {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    padding: 24rpx 0;
    .item-img {
      width: 196rpx;
      height: 144rpx;
      border-radius: 8rpx;
      margin-right: 24rpx;
      object-fit: cover;
    }
    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .item-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      .item-meta {
        font-size: 24rpx;
        color: #999;
        display: flex;
        flex-direction: column;
        gap: 4rpx;
        .item-date {
          margin-top: 2rpx;
        }
      }
    }
  }
}
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh; // 设置最小高度，确保有足够空间居中
  text-align: center;
  color: #999;
  padding: 60rpx 0;
  font-size: 28rpx;
}
</style>
