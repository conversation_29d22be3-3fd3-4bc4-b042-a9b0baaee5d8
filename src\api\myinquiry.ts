import { http } from '@/utils/http'

// 获取咨询消息列表
export function get_consultation_list(params) {
  return http.get('/h5/disease/consultation/list', params)
}

// 发送咨询消息
export function post_consultation(data: { expertId: number, images?: string, messageContent?: string }) {
  return http.post('/h5/disease/consultation', data)
}
// 获取咨询消息详情
export function get_consultation_info(expertId: number, Date?: string) {
  return http.get(`/h5/disease/consultation/${expertId}`, { Date })
}

// 评分
export function post_addEvaluation(data: { expertId: number, score: number, content?: string }) {
  return http.post('/h5/disease/consultation/addEvaluation', data)
}
