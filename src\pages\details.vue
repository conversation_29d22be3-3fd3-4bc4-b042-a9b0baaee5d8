<script>
export default {
  data() {
    return {
      list: [
        'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img1.jpeg',
        'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img2.jpeg',
        'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/img3.jpeg',
      ],
      iconList: [
        {
          text: '特色乡村',
          url: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/icon1.png',
        },
        {
          text: '名宿乡村',
          url: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/icon2.png',
        },
        {
          text: '旅游乡村',
          url: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/icon3.png',
        },
        {
          text: '乡村推荐',
          url: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/icon4.png',
        },
        {
          text: '全部乡村',
          url: 'https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/icon5.png',
        },
      ],
    }
  },
  methods: {
    onClick(text) {
      uni.navigateTo({
        url: `/pages-sub/dummyPage?text=${text}`,
      })
    },
    handleNoClick(text) {
      uni.navigateTo({
        url: `/pages-sub/dummyPage?text=${text}`,
      })
    },
  },
}
</script>

<template>
  <view class="dummy-page">
    <wd-swiper :list="list" autoplay />
    <view class="content">
      <view class="icon-box">
        <view v-for="(item, index) in iconList" :key="index" class="item" @click="onClick(item.text)">
          <image :src="item.url" mode="scaleToFill" class="img" />
          <text>{{ item.text }}</text>
        </view>
      </view>
      <view class="box1">
        <view class="top">
          <view class="top_left">
            <text class="title">
              最新资讯
            </text>
            <text>这是最新的资讯...</text>
          </view>
          <u-icon size="16" name="arrow-right" />
        </view>
        <view class="img_box">
          <image src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/shiye1.png" mode="scaleToFill" @click="onClick('公益事业')" />
          <image src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/shiye2.png" mode="scaleToFill" @click="onClick('活动招募')" />
        </view>
      </view>
      <view class="box2" @click="handleNoClick('打卡')">
        <image style="width: 100%;height: 88px;" src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/luy.png" mode="scaleToFill" />
      </view>
      <view class="box3">
        <view>最新上架</view>
        <image class="spot" style="width: 100%;height: 88px;" src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/news.png" mode="scaleToFill" />
      </view>
    </view>
  </view>
</template>

<style lang="less" scoped>
.dummy-page {
  width: 100%;
  height: 100%;

  .content {
    padding: 0 10px;
    background-color: #fff;

    .icon-box {
      display: flex;
      padding: 16px 0;
      background-color: #fff;

      .item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 12px;
        color: #333;

        .img {
          width: 54px;
          height: 54px;
          margin-bottom: 8px;
        }
      }
    }

    .box1 {
      background: #fff;
      padding: 12px;
      border-radius: 8px;
      box-shadow: 0px 2px 10px 0px #5e5e5e26;

      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .top_left {
          .title {
            color: #f4590c;
            font-size: 16px;
            margin-right: 10px;
          }
        }
      }

      .img_box {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;

        image {
          width: 48%;
          height: 100px;
          border-radius: 5px;
        }
      }
    }

    .box2 {
      margin-top: 10px;
      border-radius: 8px;
      overflow: hidden;
    }

    .box3 {
      margin-top: 10px;
      font-size: 16px;
      margin-right: 10px;
      .spot {
        margin-top: 8px;
      }
    }
  }
}
</style>
