<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { getExpertEvaluateAPI } from '@/api/listOfExperts'

const BASE_URL = import.meta.env.VITE_IMG_URL
const id = ref(0)
const totalComments = ref(0)
const pages = ref({
  pageNum: 1,
  pageSize: 10,
  expertId: '',
})
const comments = ref([
  {
    patientAvatar: '', // 头像
    patientName: '', // 患者姓名
    createTime: '', // 创建时间
    score: 0, // 评分
    content: '', // 评价内容
  },
])
// 获取患者评价列表
async function getExpertEvaluate() {
  const res = await getExpertEvaluateAPI({
    ...pages.value,
    expertId: id.value,
  })
  comments.value = (res as any).rows || []
  totalComments.value = (res as any).total
}
// 隐藏姓名
function maskName(name: string) {
  if (!name)
    return ''
  return name[0] + '*'.repeat(name.length - 1)
}
onLoad((options) => {
  id.value = options.id
  getExpertEvaluate()
})
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      更多评价
    </FgNavbar>
    <view class="comments-list">
      <text class="comment-header">
        患者评价
        <text class="comment-header-total">
          ({{ totalComments }})
        </text>
      </text>
      <view v-for="(item, index) in comments" :key="index" class="comment-list-item">
        <view class="comment-item">
          <image :src=" item.patientAvatar ? BASE_URL + item.patientAvatar : '../../static/images/avatar.jpg'" mode="scaleToFill" class="avatar" />
          <view class="comment-item-content">
            <view class="comment-item-header">
              <text class="name">
                {{ maskName(item.patientName) }}
              </text>
              <text class="comment-item-time">
                {{ item.createTime }}
              </text>
            </view>
            <wd-rate v-model="item.score" readonly active-color="#FFC107" />
          </view>
        </view>
        <view class="comment-item-text">
          {{ item.content }}
        </view>
      </view>
      <view v-if="comments.length === 0" class="empty-tip-wrapper">
        <wd-status-tip
          :image-size="{ height: 120, width: 120 }"
          image="/static/images/empty.png"
          tip="暂无评价"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  .comments-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 24rpx 32rpx;
    .comment-header {
      display: block;
      color: rgba(0, 0, 0, 0.85);
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 600;

      .comment-header-total {
        color: rgba(0, 0, 0, 0.45);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        margin-left: 8rpx;
      }
    }
    .comment-list-item {
      padding-top: 24rpx;

      .comment-item {
        display: flex;
        align-items: flex-start;
        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }
        .comment-item-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          .comment-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .name {
              font-size: 30rpx;
              font-weight: 600;
              color: #222;
            }
            .comment-item-time {
              font-size: 24rpx;
              color: #ccc;
              margin-top: 16rpx;
            }
          }
        }
      }
      .comment-item-text {
        margin-top: 16rpx;
        color: rgba(0, 0, 0, 0.85);
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid #e5e5e5;
      }
    }
    .empty-tip-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}
</style>
