<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import GlobalLoading from '@/components/global-loading/index.vue'

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars">
    <slot />
    <wd-toast />
    <wd-message-box />
    <GlobalLoading />
  </wd-config-provider>
</template>
