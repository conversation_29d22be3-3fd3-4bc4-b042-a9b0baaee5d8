import { useLoadingStore } from '@/store/loading'

/**
 * 页面级别的loading hook
 * 简化loading的使用
 */
export function usePageLoading() {
  const loadingStore = useLoadingStore()

  /**
   * 包装异步函数，自动显示和隐藏loading
   * @param asyncFn 异步函数
   */
  const withLoading = async <T>(
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    return loadingStore.withLoading(asyncFn)
  }

  /**
   * 手动显示loading
   */
  const showLoading = () => {
    loadingStore.showLoading()
  }

  /**
   * 手动隐藏loading
   */
  const hideLoading = () => {
    loadingStore.hideLoading()
  }

  return {
    withLoading,
    showLoading,
    hideLoading,
    isLoading: loadingStore.isLoading,
    loadingText: loadingStore.loadingText
  }
}
