<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from 'wot-design-uni'
import { getExpertRecordDetailAPI, saveExpertRecordAPI } from '@/api/listOfExperts'

const img_url = import.meta.env.VITE_IMG_URL

const { warning: showNotify, success: showSuccess } = useToast()
interface ExpertRecordDetail {
  currentIllness?: string
  pastHistory?: string
  personalHistory?: string
  familyHistory?: string
  allergyHistory?: string
  medicalImages?: string
  labReports?: string
  additionalFiles?: string
}

const currentIllness = ref('')
const pastHistory = ref('')
const personalHistory = ref('')
const familyHistory = ref('')
const allergyHistory = ref('')
const medicalImagesList = ref([])
const labReportsList = ref([])
const additionalFilesList = ref([])

// 上传文件change事件
function handleMedicalImagesChange(e: any) {
  medicalImagesList.value = e.fileList
}
function handleLabReportsChange(e: any) {
  labReportsList.value = e.fileList
}
function handleAdditionalFilesChange(e: any) {
  additionalFilesList.value = e.fileList
}

// 保存
async function handleSave() {
  // 判断所有内容都为空
  if (
    !currentIllness.value.trim()
    && !pastHistory.value.trim()
    && !personalHistory.value.trim()
    && !familyHistory.value.trim()
    && !allergyHistory.value.trim()
    && medicalImagesList.value.length === 0
    && labReportsList.value.length === 0
    && additionalFilesList.value.length === 0
  ) {
    showNotify({ msg: '请填写内容' })
    return
  }
  // console.log(medicalImagesList)
  // console.log(medicalImagesList.value.map((item: any) => JSON.parse(item.response).data.fileName).join(','))
  const data = {
    currentIllness: currentIllness.value,
    pastHistory: pastHistory.value,
    personalHistory: personalHistory.value,
    familyHistory: familyHistory.value,
    allergyHistory: allergyHistory.value,
    medicalImages: medicalImagesList.value
      .map((item) => {
        if (item.response) {
          try {
            return JSON.parse(item.response).data.fileName
          }
          catch {
            return ''
          }
        }
        else if (item.url) {
          // 去掉 img_url 前缀
          return item.url.replace(img_url, '')
        }
        return ''
      })
      .filter(Boolean)
      .join(','),
    labReports: labReportsList.value
      .map((item) => {
        if (item.response) {
          try {
            return JSON.parse(item.response).data.fileName
          }
          catch {
            return ''
          }
        }
        else if (item.url) {
          return item.url.replace(img_url, '')
        }
        return ''
      })
      .filter(Boolean)
      .join(','),
    additionalFiles: additionalFilesList.value
      .map((item) => {
        if (item.response) {
          try {
            return JSON.parse(item.response).data.fileName
          }
          catch {
            return ''
          }
        }
        else if (item.url) {
          return item.url.replace(img_url, '')
        }
        return ''
      })
      .filter(Boolean)
      .join(','),
  }
  try {
    // console.log(data, 12321321)
    await saveExpertRecordAPI(data)
    showSuccess({ msg: '保存成功' })
    // 清空数据
    currentIllness.value = ''
    pastHistory.value = ''
    personalHistory.value = ''
    familyHistory.value = ''
    allergyHistory.value = ''
    medicalImagesList.value = []
    labReportsList.value = []
    additionalFilesList.value = []
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
  catch (e) {
    showNotify({ msg: '保存失败' })
  }
}
// 获取患者病史记录详细信息
async function getExpertRecordDetail() {
  const res = await getExpertRecordDetailAPI()

  // 类型断言
  const data = res.data as ExpertRecordDetail || {}

  currentIllness.value = data.currentIllness || ''
  pastHistory.value = data.pastHistory || ''
  personalHistory.value = data.personalHistory || ''
  familyHistory.value = data.familyHistory || ''
  allergyHistory.value = data.allergyHistory || ''
  console.log(data.medicalImages)

  // 处理图片、文件列表
  medicalImagesList.value = (data.medicalImages || '')
    .split(',')
    .filter(Boolean)
    .map((url) => {
      return { url: img_url + url }
    })
  labReportsList.value = (data.labReports || '')
    .split(',')
    .filter(Boolean)
    .map((url) => { return { url: img_url + url } })
  additionalFilesList.value = (data.additionalFiles || '')
    .split(',')
    .filter(Boolean)
    .map((url) => { return { url: img_url + url } })
  // console.log(medicalImagesList.value)
}
onLoad(() => {
  getExpertRecordDetail()
})
const uploadBaseUrl = import.meta.env.VITE_UPLOAD_URL
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true" :z-index="99">
      病史上传
    </FgNavbar>

    <view class="upload-content">
      <text class="upload-content-item-title">
        现病史
      </text>
      <view class="upload-content-item-textarea">
        <wd-textarea v-model="currentIllness" :maxlength="120" clearable show-word-limit placeholder="请输入症状起始时间、性质、程度、演变过程、诊疗经过等..." />
      </view>
    </view>
    <view class="upload-content">
      <text class="upload-content-item-title">
        既往病史
      </text>
      <view class="upload-content-item-textarea">
        <wd-textarea v-model="pastHistory" :maxlength="120" clearable show-word-limit placeholder="请输入疾病史(含传染病)、手术史、外伤史、输血史等..." />
      </view>
    </view>
    <view class="upload-content">
      <text class="upload-content-item-title">
        个人史
      </text>
      <view class="upload-content-item-textarea">
        <wd-textarea v-model="personalHistory" :maxlength="120" clearable show-word-limit placeholder="请输入出生地、居住地、生活习惯、职业暴露、冶游史等..." />
      </view>
    </view>
    <view class="upload-content">
      <text class="upload-content-item-title">
        家族史
      </text>
      <view class="upload-content-item-textarea">
        <wd-textarea v-model="familyHistory" :maxlength="120" clearable show-word-limit placeholder="请输入三代内遗传病、类似疾病、传染病情况..." />
      </view>
    </view>
    <view class="upload-content">
      <text class="upload-content-item-title">
        过敏史
      </text>
      <view class="upload-content-item-textarea">
        <wd-textarea v-model="allergyHistory" :maxlength="120" clearable show-word-limit placeholder="请输入药物/食物过敏源及反应..." />
      </view>
    </view>
    <view class="upload-img-content">
      <text class="upload-content-item-title">
        病例照片
      </text>
      <view class="upload-content-item-upload upload">
        <wd-upload
          :file-list="medicalImagesList"
          :limit="3"
          :action="uploadBaseUrl"
          @change="handleMedicalImagesChange"
        />
      </view>
    </view>
    <view class="upload-img-content">
      <text class="upload-content-item-title">
        化验单
      </text>
      <view class="upload-content-item-upload upload">
        <wd-upload
          :file-list="labReportsList"
          :limit="3"
          :action="uploadBaseUrl"
          @change="handleLabReportsChange"
        />
      </view>
    </view>
    <view class="upload-img-content">
      <text class="upload-content-item-title">
        其他资料
      </text>
      <view class="upload-content-item-upload upload">
        <wd-upload
          :file-list="additionalFilesList"
          :limit="3"
          :action="uploadBaseUrl"
          @change="handleAdditionalFilesChange"
        />
      </view>
    </view>
    <view class="bottom-btn-wrap">
      <button class="consult-btn" @click="handleSave">
        保存
      </button>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #f5f5f5;
  padding-bottom: 165rpx;
  .upload-content {
    padding: 24rpx 32rpx 0 32rpx;
    .upload-content-item-title {
      color: rgba(0, 0, 0, 0.85);
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      display: block;
      margin-bottom: 16rpx;
    }
    .upload {
      background-color: #fff;
      padding: 32rpx 24rpx;
    }
  }
  .upload-img-content {
    .upload-content-item-title {
      color: rgba(0, 0, 0, 0.85);
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      display: block;
      margin-bottom: 16rpx;
      padding: 24rpx 32rpx 0 32rpx;
    }
    .upload {
      background-color: #fff;
      padding: 32rpx 24rpx 0 32rpx;
    }
  }
  .bottom-btn-wrap {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 24rpx 0 48rpx 0;
    background: #fff;
    border-bottom-left-radius: 32rpx;
    border-bottom-right-radius: 32rpx;
    box-shadow: 0 -8rpx 24rpx 0 rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;
    z-index: 10;

    .consult-btn {
      width: 90vw;
      height: 88rpx;
      background: #1890ff;
      color: #fff;
      border-radius: 44rpx;
      font-size: 32rpx;
      text-align: center;
      line-height: 88rpx;
      border: none;
    }
  }
}
</style>
