// // 获取就诊人信息列表
// export function getPatientListAPI() {
//   return http.get('/h5/disease/patient/list')
// }
// export function ossUpload(file: File) {
//   console.log(file)
//   const formData = new FormData()
//   console.log(1111)

//   formData.append('file', file)
//   return http.post('/resource/oss/upload', formData, {
//     headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
//     timeout: 30 * 1000,
//   })
// }

// export function ossUpload(filePath: string) {
//   console.log(123123) // 保留原函数日志行为

//   console.log(filePath) // 保留原函数日志行为

//   return new Promise((resolve, reject) => {
//     uni.uploadFile({
//       url: '/resource/oss/upload', // 接口 URL
//       filePath, // 小程序临时文件路径
//       name: 'file', // 表单字段名（与原 FormData 保持一致）
//       header: {
//         'Content-Type': 'multipart/form-data;charset=UTF-8', // 保持与原函数一致的请求头
//       },
//       timeout: 30 * 1000, // 超时时间（30秒）
//       success: (res) => {
//         try {
//           // 尝试将返回数据解析为 JSON（如果后端返回 JSON 格式）
//           const data = JSON.parse(res.data)
//           resolve(data)
//         }
//         catch (error) {
//           // 如果解析失败，直接返回原始数据
//           resolve(res.data)
//         }
//       },
//       fail: (err) => {
//         reject(err)
//       },
//     })
//   })
// }

// utils/oss-service.ts
// import { http } from '@/utils/http'

const ContentTypeEnum = {
  // form-data  upload
  FORM_DATA: 'multipart/form-data;charset=UTF-8',
  // form-data qs
  FORM_URLENCODED: 'application/x-www-form-urlencoded;charset=UTF-8',
  // json
  JSON: 'application/json;charset=UTF-8',
} as const

// export function ossUpload(filePath: string) {
//   // 创建 FormData 对象
//   const formData = new FormData()
//   formData.append('file', filePath) // 假设 filePath 可直接放入 FormData（实际可能需要处理）

//   return http.post('/resource/oss/upload', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data', // 关键：指定表单上传
//     },
//     // 可选：监听上传进度
//     onUploadProgress: (progressEvent) => {
//       const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
//       console.log('上传进度:', percentCompleted)
//     },
const uploadBaseUrl = import.meta.env.VITE_UPLOAD_URL
export function ossUpload(filePath) {
  // 在调用ossUpload前添加
  uni.getFileSystemManager().access({
    path: filePath,
    success: () => console.log('文件可访问'),
    fail: () => console.error('文件不可访问'),
  })
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: uploadBaseUrl, // 使用完整URL
      filePath,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data',
        'X-Requested-With': 'XMLHttpRequest', // 保持与PC端一致
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
            resolve(data)
          }
          catch (e) {
            resolve(res.data)
          }
        }
        else {
          reject(new Error(`上传失败，状态码：${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(err)
      },
    })
  })
}
