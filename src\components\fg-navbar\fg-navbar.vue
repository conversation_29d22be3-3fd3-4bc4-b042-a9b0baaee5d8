<script lang="ts" setup>
// 添加默认导出
defineOptions({
  name: 'FgNavbar',
})

const props = withDefaults(
  defineProps<{
    leftText?: string
    rightText?: string
    leftArrow?: boolean
    bordered?: boolean
    fixed?: boolean
    placeholder?: boolean
    zIndex?: number
    safeAreaInsetTop?: boolean
    leftDisabled?: boolean
    rightDisabled?: boolean
    customClickLeft?: boolean // 新增：是否使用自定义左侧点击处理
  }>(),
  {
    leftText: '返回',
    rightText: '',
    leftArrow: true,
    bordered: true,
    fixed: false,
    placeholder: true,
    zIndex: 1,
    safeAreaInsetTop: true,
    leftDisabled: false,
    rightDisabled: false,
    customClickLeft: false,
  },
)

// 定义事件
const emit = defineEmits<{
  'click-left': []
  'click-right': []
}>()

function handleClickLeft() {
  // 先触发父组件的事件
  emit('click-left')

  // 如果没有启用自定义处理，则执行默认行为
  if (!props.customClickLeft) {
    uni.navigateBack({
      fail() {
        uni.reLaunch({
          url: '/pages/index/index',
        })
      },
    })
  }
}

function handleClickRight() {
  // 触发父组件的右侧点击事件
  emit('click-right')
}
</script>

<template>
  <wd-navbar
    :left-text="leftText"
    :right-text="rightText"
    :left-arrow="leftArrow"
    :bordered="bordered"
    :fixed="fixed"
    :placeholder="placeholder"
    :z-index="zIndex"
    :safe-area-inset-top="safeAreaInsetTop"
    :left-disabled="leftDisabled"
    :right-disabled="rightDisabled"
    @click-left="handleClickLeft"
    @click-right="handleClickRight"
  >
    <template #title>
      <slot />
    </template>
  </wd-navbar>
</template>
