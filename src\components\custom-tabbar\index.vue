<script lang="ts">
import { onShow } from '@dcloudio/uni-app'
import { computed, defineComponent, onMounted, ref } from 'vue'

// 定义tabbar项的类型
interface TabbarItem {
  pagePath: string
  iconPath: string
  selectedIconPath: string
  text: string
}

const audit = ref(0)

export default defineComponent({
  name: 'CustomTabbar',
  setup() {
    // 当前所选导航栏
    const curItem = ref('/pages/index/index')
    // 消息红点状态
    const showRedDot = ref(false)

    // 导航栏列表数据
    const tabbarListData = ref<TabbarItem[]>([
      {
        pagePath: '/pages/index/index',
        iconPath: '/static/tabbar/home.png',
        selectedIconPath: '/static/tabbar/homeHL.png',
        text: '首页',
      },
      {
        pagePath: '/pages/listOfExperts/index',
        iconPath: '/static/tabbar/list.png',
        selectedIconPath: '/static/tabbar/listHL.png',
        text: '专家列表',
      },
      {
        pagePath: '/pages/Myinquiry/index',
        iconPath: '/static/tabbar/consult.png',
        selectedIconPath: '/static/tabbar/consultHL.png',
        text: '我的问诊',
      },
      {
        pagePath: '/pages/my/index',
        iconPath: '/static/tabbar/my.png',
        selectedIconPath: '/static/tabbar/myHL.png',
        text: '个人中心',
      },
    ])

    const tabbarTravelData = ref<TabbarItem[]>([
      {
        pagePath: '/pages/index/index',
        iconPath: '/static/tabbar/home.png',
        selectedIconPath: '/static/tabbar/homeHL.png',
        text: '首页',
      },
      {
        pagePath: '/pages/my/index',
        iconPath: '/static/tabbar/my.png',
        selectedIconPath: '/static/tabbar/myHL.png',
        text: '个人中心',
      },
    ])
    // 获取本地存储的audit
    audit.value = uni.getStorageSync('audit')

    // 根据 props.tabbarType 计算当前使用的 tabbar 数据
    const currentTabbarData = computed(() => {
      return audit.value === 0 || String(audit.value) === '0' ? tabbarTravelData.value : tabbarListData.value
    })

    // 更新当前页面路径
    // 定义哪些页面应该显示tabbar
    const tabbarPages = [
      '/pages/index/index',
      '/pages/listOfExperts/index',
      '/pages/Myinquiry/index',
      '/pages/my/index',
    ]

    // 当前页面是否应该显示tabbar
    const shouldShowTabbar = ref(true)

    function updateCurrentPage() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const currentPath = `/${currentPage.route}`
        curItem.value = currentPath

        // 检查当前页面是否应该显示tabbar
        shouldShowTabbar.value = tabbarPages.includes(currentPath)

        console.log('当前页面路径:', currentPath, '是否显示tabbar:', shouldShowTabbar.value)
      }
    }

    onMounted(() => {
      audit.value = uni.getStorageSync('audit')
      console.log('tabbar audit.value:', audit.value, typeof audit.value)
      updateCurrentPage()
      // 不需要隐藏原生tabBar，因为pages.json中已经设置了custom: true

      // 监听消息更新事件
      uni.$on('messageCountUpdated', (count: number) => {
        showRedDot.value = count === 0 // count为0时显示红点
      })
    })

    onShow(() => {
      audit.value = uni.getStorageSync('audit')
      updateCurrentPage()
    })

    /* 导航栏切换 */
    function changeItem(item: TabbarItem) {
      curItem.value = item.pagePath
      uni.switchTab({
        url: item.pagePath,
      })
    }

    return {
      curItem,
      currentTabbarData,
      changeItem,
      showRedDot,
      shouldShowTabbar,
    }
  },
})
</script>

<template>
  <view v-if="shouldShowTabbar" class="custom-tabbar">
    <view class="tabbar-container">
      <view v-for="(item, index) in currentTabbarData" :key="index" class="tabbar-item" @click="changeItem(item)">
        <view class="item-icon">
          <image :src="curItem === item.pagePath ? item.selectedIconPath : item.iconPath" class="icon-img" />
          <!-- 红点显示，只在个人中心显示 -->
          <view v-if="item.pagePath === '/pages/my/index' && showRedDot" class="red-dot" />
        </view>
        <view class="item-text" :class="{ active: curItem === item.pagePath }">
          {{ item.text }}
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-top: 1px solid #e7e7e7;
  padding-bottom: env(safe-area-inset-bottom);
}

.debug-info {
  background: red;
  color: white;
  text-align: center;
  padding: 10rpx;
  font-size: 24rpx;
}

.tabbar-container {
  display: flex;
  align-items: center;
  gap: 84rpx;
  padding: 0 57rpx;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  width: 96rpx;
  height: 84rpx;
}

.item-icon {
  position: relative;
  width: 52rpx;
  height: 52rpx;
  // margin-bottom: 8rpx;
}

.icon-img {
  width: 100%;
  height: 100%;
}

.red-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.item-text {
  font-size: 24rpx;
  color: #4f6272;

  &.active {
    color: #007fee;
    font-weight: 600;
  }
}
</style>
