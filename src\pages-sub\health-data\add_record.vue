<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { useToast } from 'wot-design-uni'
import {
  addBloodOxygenRecordAPI,
  addBloodPressureRecordAPI,
  addBloodSugarRecordAPI,
  addBodyFatRateRecordAPI,
  addHeartRateRecordAPI,
  addHeightWeightRecordAPI,
} from '@/api/health-data'
import { getPatientListAPI } from '@/api/patients'
import FgNavbar from '@/components/fg-navbar/fg-navbar.vue'
import { useUserStore } from '@/store/user'

const { warning: showNotify, success: showSuccess } = useToast()
const type = ref('')
const value = ref() // 初始化为当前时间
const form = ref()
const userStore = useUserStore()

const model = reactive<any>({})

onLoad((options) => {
  type.value = options.type || ''
  // 初始化表单字段
  switch (type.value) {
    case 'bloodPressure':
      model.high = ''
      model.low = ''
      break
    case 'bloodSugar':
      model.sugar = ''
      break
    case 'heartRate':
      model.heartRate = ''
      break
    case 'bodyFatRate':
      model.bodyFatRate = ''
      break
    case 'bloodOxygen':
      model.arterial = ''
      model.venous = ''
      break
    case 'heightWeight':
      model.height = ''
      model.weight = ''
      break
  }
})

onShow(async () => {
  // 如果 patientId 为空，主动获取一次
  if (!userStore.patientId) {
    const res = await getPatientListAPI()
    if (Array.isArray(res.data)) {
      const selectedPatient = res.data.find((item: any) => item.selected === 1)
      if (selectedPatient) {
        userStore.patientId = selectedPatient.id
      }
    }
  }
})
// 时间选择
function handleConfirm({ value }) {
  console.log(new Date(value))
}
// 保存记录
function handleSubmit() {
  form.value.validate().then(({ valid }) => {
    if (valid) {
      let data = {}
      switch (type.value) {
        case 'bloodPressure':
          data = {
            systolic: model.high,
            diastolic: model.low,
            measureTime: value.value,
          }
          break
        case 'bloodSugar':
          data = {
            glucose: model.sugar,
            measureTime: value.value,
          }
          break
        case 'heartRate':
          data = {
            rate: model.heartRate,
            measureTime: value.value,
          }
          break
        case 'bodyFatRate':
          data = {
            fatRate: model.bodyFatRate,
            patientId: userStore.patientId, // 仅体脂率需要 patientId
            measureTime: value.value,
          }
          break
        case 'bloodOxygen':
          data = {
            arterialOxygen: model.arterial,
            venousOxygen: model.venous,
            measureTime: value.value,
          }
          break
        case 'heightWeight':
          data = {
            height: model.height,
            weight: model.weight,
            measureTime: value.value,
          }
          break
      }
      // 根据type调用不同接口
      let apiPromise: Promise<any> | null = null
      switch (type.value) {
        case 'bloodPressure':
          apiPromise = addBloodPressureRecordAPI(data)
          break
        case 'bloodSugar':
          apiPromise = addBloodSugarRecordAPI(data)
          break
        case 'heartRate':
          apiPromise = addHeartRateRecordAPI(data)
          break
        case 'bodyFatRate':
          apiPromise = addBodyFatRateRecordAPI(data)
          break
        case 'bloodOxygen':
          apiPromise = addBloodOxygenRecordAPI(data)
          break
        case 'heightWeight':
          apiPromise = addHeightWeightRecordAPI(data)
          break
      }
      if (apiPromise) {
        apiPromise
          .then(() => {
            uni.$emit && uni.$emit('refreshHealthData')
            showSuccess({ msg: '增加成功' })
            // 延迟跳转，确保提示能够显示
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages-sub/health-data/index',
              })
            }, 1500)
          })
          .catch(() => {
            showNotify({ msg: '增加失败' })
          })
      }
    }
  })
}
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      健康数据
    </FgNavbar>
    <wd-form ref="form" :model="model">
      <wd-datetime-picker
        v-model="value"
        type="datetime"
        :max-date="new Date().getTime()"
        label="记录时间"
        @confirm="handleConfirm"
      />
      <wd-cell-group border>
        <wd-input
          v-if="type === 'bloodPressure'"
          v-model="model.high"
          label="高压(收缩压)(mmHg)"
          label-width="226rpx"
          prop="high"
          type="number"
          clearable
          placeholder="请输入高压"
        />
        <wd-input
          v-if="type === 'bloodPressure'"
          v-model="model.low"
          label="低压(舒张压)(mmHg)"
          label-width="226rpx"
          prop="low"
          type="number"
          clearable
          placeholder="请输入低压"
        />
        <wd-input
          v-if="type === 'bloodSugar'"
          v-model="model.sugar"
          label="血糖(mmol/L)"
          type="digit"
          label-width="226rpx"
          prop="sugar"
          clearable
          placeholder="请输入血糖值"
        />
        <wd-input
          v-if="type === 'heartRate'"
          v-model="model.heartRate"
          label="心率(次/分)"
          type="number"
          label-width="226rpx"
          prop="heartRate"
          clearable
          placeholder="请输入心率"
        />
        <wd-input
          v-if="type === 'bodyFatRate'"
          v-model="model.bodyFatRate"
          label="体脂率(%)"
          type="digit"
          label-width="226rpx"
          prop="bodyFatRate"
          clearable
          placeholder="请输入体脂率"
        />
        <wd-input
          v-if="type === 'bloodOxygen'"
          v-model="model.arterial"
          label="动脉血氧(%)"
          type="digit"
          label-width="226rpx"
          prop="arterial"
          clearable
          placeholder="请输入动脉血氧"
        />
        <wd-input
          v-if="type === 'bloodOxygen'"
          v-model="model.venous"
          label="静脉血氧(%)"
          type="digit"
          label-width="226rpx"
          prop="venous"
          clearable
          placeholder="请输入静脉血氧"
        />
        <wd-input
          v-if="type === 'heightWeight'"
          v-model="model.height"
          label="身高(cm)"
          type="digit"
          label-width="226rpx"
          prop="height"
          clearable
          placeholder="请输入身高"
        />
        <wd-input
          v-if="type === 'heightWeight'"
          v-model="model.weight"
          label="体重(kg)"
          type="digit"
          label-width="226rpx"
          prop="weight"
          clearable
          placeholder="请输入体重"
        />
      </wd-cell-group>
      <view v-if="type === 'bloodPressure'" class="normal-range">
        <text class="normal-range-title">
          指标正常范围
        </text>
        <view>
          <text class="normal-range-text-high">
            高压:90～139mmHg
          </text>
          <text class="normal-range-text-low">
            低压:60～89mmHg
          </text>
        </view>
      </view>
      <view v-if="type === 'bloodOxygen'" class="normal-range">
        <text class="normal-range-title">
          指标正常范围
        </text>
        <view>
          <text class="normal-range-text-high">
            动脉血氧95~98%
          </text>
          <text class="normal-range-text-low">
            静脉血氧64~88%
          </text>
        </view>
      </view>
      <view v-if="type === 'heartRate'" class="normal-range">
        <text class="normal-range-title">
          指标正常范围
        </text>
        <view>
          <text class="normal-range-text-high">
            60~100次/分
          </text>
        </view>
      </view>
      <view v-if="type === 'bodyFatRate'" class="normal-range">
        <text class="normal-range-title">
          指标正常范围
        </text>
        <view>
          <text class="normal-range-text-high">
            男性6%~20%
          </text>
          <text class="normal-range-text-low">
            女性20%~30%
          </text>
        </view>
      </view>
      <view v-if="type === 'bloodSugar'" class="normal-range">
        <text class="normal-range-title">
          指标正常范围
        </text>
        <view>
          <text class="normal-range-text-high">
            空腹血糖3.9-6.1mmol/L
          </text>
        </view>
        <view>
          <text class="normal-range-text-high">
            餐后1小时6.7-9.4mmol/L
          </text>
        </view>
        <view>
          <text class="normal-range-text-high">
            餐后2小时≤7.8mmol/L
          </text>
        </view>
      </view>
    </wd-form>
    <view class="bottom-btn-bar">
      <view class="add-record-btn" @click="handleSubmit">
        保存记录
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  padding: 24rpx 0;
  min-height: 90vh;
  .normal-range {
    font-size: 28rpx;
    padding: 24rpx 32rpx;
    font-family: 'PingFang SC';
    .normal-range-title {
      color: rgba(0, 0, 0, 0.45);
      margin-bottom: 16rpx;
    }
    .normal-range-text-high {
      color: #008aff;
      margin-right: 32rpx;
    }
    .normal-range-text-low {
      color: #008aff;
    }
  }
  .bottom-btn-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 32rpx;
    display: flex;
    justify-content: center;
    z-index: 10;
    background: #fff;
    padding: 32rpx;
    .add-record-btn {
      width: 90vw;
      background: #008aff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      border: none;
      font-weight: bold;
      box-shadow: 0 4rpx 16rpx 0 rgba(0, 138, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 0;
    }
  }
}
</style>
