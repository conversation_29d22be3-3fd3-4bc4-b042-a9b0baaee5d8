<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { onLoad, onUnload } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import { get_consultation_info, post_addEvaluation, post_consultation } from '@/api/myinquiry'
import { ossUpload } from '@/api/uploda'
import { useLoadingStore } from '@/store'
import { toast } from '@/utils/toast'
import FgNavbar from '../../components/fg-navbar/fg-navbar.vue'

// 页面loading
const loadingStore = useLoadingStore()

const id = ref()
const expert_data = ref<{ name: string, avatar: string, id: number, title: string }>()
// 删除标志：0->未删除；1->已删除
const delFlag = ref(0)

const messages = ref<
  {
    id?: number // 新增id字段，便于去重
    createTime?: string
    /**
     * 复诊时间
     */
    followUpTime?: string
    /**
     * 多图(存储图片URL数组)
     */
    images?: string
    /**
     * 消息内容
     */
    messageContent?: string
    /**
     * 状态：0未读 1已读
     */
    status?: number
    /**
     * 类型(1患者 2专家)
     */
    type?: number
  }[]
>([

])
const showRemind = ref(false)
let remindTimer: number | null = null
function showSensitiveRemind() {
  showRemind.value = true
  if (remindTimer)
    clearTimeout(remindTimer)
  remindTimer = setTimeout(() => {
    showRemind.value = false
    remindTimer = null
  }, 5000)
}

// 动态获取状态栏和导航栏高度
const remindTop = ref(64) // 默认值
const inputBarBottom = ref('0px')
// 用户头像
const userAvatar = ref('')

onMounted(() => {
  const info = uni.getStorageSync('userInfo')
  userAvatar.value = info.avatar
  // 动态获取状态栏和导航栏高度
  const systemInfo = uni.getSystemInfoSync()
  const statusBarHeight = systemInfo.statusBarHeight || 20
  const navBarHeight = 44
  remindTop.value = statusBarHeight + navBarHeight

  // #ifdef MP-WEIXIN
  uni.onKeyboardHeightChange((res) => {
    console.log('键盘高度变化:', res.height, '设备平台:', systemInfo.platform)

    if (res.height > 0) {
      // 键盘弹起 - 直接设置高度，不区分平台
      inputBarBottom.value = `${res.height}px`

      // 延迟滚动确保页面内容可见
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 999999,
          duration: 200,
        })
      }, 100)
    }
    else {
      // 键盘收起
      inputBarBottom.value = '0px'
    }
  })
  // #endif
})

onUnmounted(() => {
  // #ifdef MP-WEIXIN
  uni.offKeyboardHeightChange && uni.offKeyboardHeightChange()
  // #endif
})
const inputValue = ref('')
// 自动滚动锚点id，初始值
const bottomId = ref('chat-bottom')

// 输入框焦点处理
function handleInputFocus() {
  console.log('输入框获得焦点')
  // 简化处理：延迟滚动确保内容可见
  setTimeout(() => {
    uni.pageScrollTo({
      scrollTop: 999999,
      duration: 200,
    })
  }, 300)
}

function handleInputBlur() {
  console.log('输入框失去焦点')
  // 输入框失焦时，确保输入框回到底部
  setTimeout(() => {
    inputBarBottom.value = '0px'
  }, 100)
}
// 选择图片
function chooseImage() {
  // 如果已删除，禁止上传图片
  if (delFlag.value === 1) {
    toast.error('该问诊记录已删除，无法上传图片')
    return
  }

  uni.chooseImage({
    count: 1, // 最多选择1张
    success: (res) => {
      // console.log(213213)
      const tempFilePath = res.tempFilePaths[0]
      // console.log(tempFilePath)

      ossUpload(tempFilePath).then((res_img: any) => {
        // console.log(res)
        const data = {
          expertId: expert_data.value.id,
          images: res_img.data.fileName,
          // messageContent: inputValue.value,
        }
        post_consultation(data).then((res: any) => {
          // 判断是否有敏感词提示
          if (res.data && typeof res.data === 'string' && res.data.includes('敏感词')) {
            showSensitiveRemind()
          }
          else {
            showRemind.value = false
          }
          get_info()
        })
      })
    },
    fail: (err) => {
      console.error('选择图片失败', err)
    },
  })
}
// 发送消息
function sendMessage() {
  if (!inputValue.value.trim())
    return

  // 如果已删除，禁止发送消息
  if (delFlag.value === 1) {
    toast.error('该问诊记录已删除，无法发送消息')
    return
  }

  const data = {
    expertId: expert_data.value.id,
    messageContent: inputValue.value,
  }
  post_consultation(data).then((res: any) => {
    // 判断是否有敏感词提示
    if (res.data && typeof res.data === 'string' && res.data.includes('敏感词')) {
      showSensitiveRemind()
    }
    else {
      showRemind.value = false
    }
    get_info().then(() => {
      nextTick(() => {
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: 9999999,
            duration: 50,
          })
        }, 50)
      })
    })
  })
  inputValue.value = ''
}
// 预览图片
function previewImage(url: string) {
  console.log(url)
  uni.previewImage({
    current: url, // 当前预览的图片
    urls: [url], // 图片列表（可扩展为多图）
  })
}
// 打开评价
const show = ref(false)
function openEvaluate() {
  show.value = true
}
// 关闭评价
function handleClose() {
  show.value = false
}
// 评价
const value = ref(0)
function handleChange(value: number) {
  // console.log(value)
}
// 评价内容
const value1 = ref('')
// 确认评价
function handleConfirm() {
  console.log('确认评价')
  if (value.value < 1) {
    return toast.error('请选择评分')
  }
  const data = {
    expertId: expert_data.value.id,
    score: value.value,
    content: value1.value,
  }
  console.log(data)
  post_addEvaluation(data).then(() => {
    toast.success('评价成功')
    show.value = false
  })
}

// 是否可评价
const evaluable = ref<boolean>()
// 获取咨询信息
function get_info() {
  // 获取最后一条消息的 createTime
  const lastMsg = messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  const lastTime = lastMsg?.createTime
  return get_consultation_info(id.value, lastTime).then((res: any) => {
    let hasNewMsg = false
    if (!lastTime) {
      messages.value = res.data.consultations || []
      hasNewMsg = messages.value.length > 0
    }
    else if (res.data.consultations && res.data.consultations.length > 0) {
      // 只追加未出现过的消息，去重
      const existIds = new Set(messages.value.map(m => m.id || m.createTime))
      const newMsgs = res.data.consultations.filter(m => !existIds.has(m.id || m.createTime))
      if (newMsgs.length > 0) {
        messages.value = messages.value.concat(newMsgs)
        hasNewMsg = true
        // 新增：如果有医生新消息，把自己发的未读消息设为已读
        const hasDoctorMsg = newMsgs.some(m => m.type === 2)
        if (hasDoctorMsg) {
          messages.value = messages.value.map((m) => {
            if (m.type === 1 && m.status === 0) {
              return { ...m, status: 1 }
            }
            return m
          })
        }
      }
    }
    // 确保 expert_data 有值，避免白屏
    if (res.data.expert) {
      expert_data.value = res.data.expert
    }
    else if (!expert_data.value) {
      expert_data.value = { name: '专家', avatar: '', id: id.value, title: '医生' }
    }
    evaluable.value = res.data.evaluable === 0
    // 获取删除标志
    delFlag.value = res.data.expert.delFlag || 0
    if (hasNewMsg) {
      nextTick(() => {
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: 9999999, // 足够大的值
            duration: 50,
          })
        }, 50)
      })
    }
  }).catch((error) => {
    console.error('获取咨询信息失败:', error)
    // 静默处理错误，避免影响用户体验
  })
}
// 是否显示时间
function shouldShowTime(index: number) {
  if (index === 0)
    return true
  const prev = messages.value[index - 1]
  const curr = messages.value[index]
  if (!prev.createTime || !curr.createTime)
    return false
  const prevTime = dayjs(prev.createTime)
  const currTime = dayjs(curr.createTime)
  // 超过5分钟或跨天就显示
  return (
    !prevTime.isSame(currTime, 'day')
    || currTime.diff(prevTime, 'minute') > 5
  )
}
function scrollToBottom() {
  nextTick(() => {
    // 动态更新锚点ID确保滚动生效
    bottomId.value = `chat-bottom-${Date.now()}`
  })
}
let timer: number | null = null
onLoad((options) => {
  id.value = options.id
  loadingStore.withLoading(async () => {
    try {
      const res: any = await get_consultation_info(id.value)
      messages.value = res.data.consultations || []
      expert_data.value = res.data.expert || { name: '专家', avatar: '', id: id.value, title: '医生' }
      evaluable.value = res.data.evaluable === 0
      // 获取删除标志
      delFlag.value = res.data.expert.delFlag || 0
      nextTick(() => {
        setTimeout(() => {
          // const height = this.data.projectNum * 90 + 92
          uni.pageScrollTo({
            scrollTop: 9999999, // 足够大的值
            duration: 300,
          })
          // scrollToBottom()
        }, 300)
      })
    }
    catch (error) {
      console.error('获取咨询信息失败:', error)
      // 设置默认值，避免白屏
      messages.value = []
      expert_data.value = { name: '专家', avatar: '', id: id.value, title: '医生' }
      evaluable.value = false
      toast.error('获取咨询信息失败，请稍后重试')
    }
  })

  timer = setInterval(() => {
    get_info()
  }, 5000)
})

onUnload(() => {
  if (timer)
    clearInterval(timer)
})
const img_url = import.meta.env.VITE_IMG_URL

function getAvatar(msg: any) {
  if (msg.type === 1) {
    // 用户头像：如果用户头像存在则使用，否则使用默认头像
    return userAvatar.value ? img_url + userAvatar.value : '../../static/images/avatar.jpg'
  }
  else if (msg.type === 2) {
    // 专家头像：如果专家头像存在则使用，否则使用默认头像
    return expert_data.value?.avatar ? img_url + expert_data.value.avatar : '../../static/images/avatar.jpg'
  }
  return '../../static/images/avatar.jpg'
}
</script>

<template>
  <view class="page">
    <!-- 固定导航栏 -->
    <view style="position: fixed; top: 0; left: 0; right: 0; z-index: 1000;">
      <FgNavbar :left-arrow="true" left-text="" :fixed="true">
        咨询专家
      </FgNavbar>
    </view>
    <!-- 提醒 -->
    <view v-if="showRemind" class="evaluate-bar-remind" :style="{ top: `${remindTop}px` }">
      <view>
        <image src="../../static/svg/remind.svg" mode="aspectFill" class="icon-remind" />
        检测到，您输入的信息存在敏感词，系统已屏蔽关键词！
      </view>
    </view>
    <!-- 评价栏 -->
    <view v-if="evaluable" class="evaluate-bar" :style="{ top: `${remindTop}px` }">
      <view class="evaluate-bar-info">
        <image :src="expert_data.avatar ? img_url + expert_data.avatar : '../../static/images/avatar.jpg'" mode="aspectFill" class="avatar" />
        <view class="evaluate-bar-text">
          <view class="evaluate-bar-name">
            {{ expert_data.name }}
          </view>
          <view class="evaluate-bar-meta">
            {{ expert_data.title }}
          </view>
        </view>
      </view>
      <view class="evaluate-bar-action" @click="openEvaluate">
        立即评价
      </view>
    </view>

    <!-- 消息列表 -->
    <view v-if="expert_data" class="chat-page mt-[20px]">
      <scroll-view class="chat-list" scroll-y :scroll-into-view="bottomId" :scroll-with-animation="true">
        <view class="chat-list-inner" :style="{ paddingTop: evaluable ? `${remindTop + 88}px` : `${remindTop}px` }">
          <!-- 增加顶部内边距，避免被导航栏和评价栏遮挡 -->
          <template v-for="(msg, idx) in messages" :key="msg.createTime">
            <!-- 分组时间，单独一行，居中 -->
            <view v-if="shouldShowTime(idx) && msg.createTime" class="chat-time">
              {{ msg.createTime }}
            </view>
            <view class="chat-item" :class="msg.type === 1 ? 'user' : 'doctor'">
              <image :src="getAvatar(msg)" class="chat-avatar" />
              <!-- <image :src="img_url + expert_data.avatar" class="chat-avatar" /> -->
              <view class="chat-content">
                <view v-if="msg.type === 2" class="chat-name">
                  {{ expert_data.name }}
                </view>
                <!-- 用户消息：未读在气泡前面 -->
                <view v-if="msg.type === 1 && msg.messageContent" class="chat-bubble-row user">
                  <view class="chat-status-meta">
                    <text class="chat-status" :style="{ color: msg.status === 0 ? '#007FEE' : '#bfbfbf' }">
                      {{ msg.status === 1 ? '已读' : '未读' }}
                    </text>
                  </view>
                  <view class="chat-bubble">
                    {{ msg.messageContent }}
                  </view>
                </view>
                <!-- 医生消息：未读在气泡后面 -->
                <view v-else-if="msg.type === 2 && msg.messageContent" class="chat-bubble-row doctor">
                  <view class="chat-bubble mt-[10px]">
                    {{ msg.messageContent }}
                  </view>
                  <view class="chat-status-meta">
                    <text class="chat-status" :style="{ color: msg.status === 0 ? '#007FEE' : '#bfbfbf' }">
                      {{ msg.status === 1 ? '已读' : '未读' }}
                    </text>
                  </view>
                </view>
                <!-- 图片消息：用户 -->
                <view v-else-if="msg.type === 1 && !msg.messageContent" class="chat-bubble-row user">
                  <view class="chat-status-meta">
                    <text class="chat-status" :style="{ color: msg.status === 0 ? '#007FEE' : '#bfbfbf' }">
                      {{ msg.status === 1 ? '已读' : '未读' }}
                    </text>
                  </view>
                  <image :src="img_url + msg.images" class="chat-image" @click="previewImage(img_url + msg.images)" />
                </view>
                <!-- 图片消息：医生 -->
                <view v-else-if="msg.type === 2 && !msg.messageContent && msg.images" class="chat-bubble-row doctor">
                  <image
                    v-for="item in msg.images.split(',')" :key="item" :src="img_url + item" class="chat-image"
                    @click="previewImage(img_url + item)"
                  />
                  <view class="chat-status-meta">
                    <text class="chat-status" :style="{ color: msg.status === 0 ? '#007FEE' : '#bfbfbf' }">
                      {{ msg.status === 1 ? '已读' : '未读' }}
                    </text>
                  </view>
                </view>
                <view v-else-if="msg.type === 2 && msg.followUpTime" class="chat-bubble-row doctor">
                  <view class="chat-bubble mt-[10px]">
                    复查复诊提醒：{{ msg.followUpTime.split(' ')[0] }}
                  </view>
                  <view class="chat-status-meta">
                    <text class="chat-status" :style="{ color: msg.status === 0 ? '#007FEE' : '#bfbfbf' }">
                      {{ msg.status === 1 ? '已读' : '未读' }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <view :id="bottomId" class="h-[1rpx]" />
          <view style="height: 100rpx;" /> <!-- 占位，防止被输入栏遮挡 -->
        </view>
      </scroll-view>
      <!-- 输入栏 -->
      <view class="chat-input-bar" :style="{ bottom: inputBarBottom }">
        <image
          src="../../static/svg/img.svg"
          mode="aspectFill"
          class="icon-img"
          :class="{ disabled: delFlag === 1 }"
          @click="chooseImage"
        />
        <textarea
          v-model="inputValue"
          class="chat-input"
          :class="{ disabled: delFlag === 1 }"
          :placeholder="delFlag === 1 ? '该问诊记录已删除，无法发送消息' : '请输入咨询问题'"
          :auto-height="true"
          :maxlength="500"
          :show-confirm-bar="false"
          :adjust-position="false"
          :cursor-spacing="10"
          :hold-keyboard="false"
          :disable-default-padding="true"
          :disabled="delFlag === 1"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
        <button
          class="chat-send-btn"
          :class="{ disabled: delFlag === 1 }"
          @click="sendMessage"
        >
          发送
        </button>
      </view>
    </view>
    <!-- 弹出评价 -->
    <wd-popup
      v-model="show" position="bottom" closable custom-style="border-radius: 14rpx 14rpx 0 0;"
      @close="handleClose"
    >
      <view class="evaluate-popup">
        <view class="evaluate-popup-title">
          评价
        </view>
        <view class="evaluate-popup-content">
          <image :src="expert_data.avatar ? img_url + expert_data.avatar : '../../static/images/avatar.jpg'" mode="aspectFill" class="evaluate-popup-image" />
          <view class="evaluate-popup-content-text">
            <view class="evaluate-popup-content-text-title">
              评价专家
            </view>
            <wd-rate v-model="value" size="25px" @change="handleChange" />
          </view>
        </view>
        <view class="evaluate-popup-textarea">
          <wd-textarea v-model="value1" placeholder="请填写评价" :maxlength="120" clearable show-word-limit custom-textarea-class="textarea_bg " custom-class="textarea_bg" custom-textarea-container-class="textarea_bg" />
        </view>
        <view class="evaluate-popup-btn">
          <view class="evaluate-popup-btn-cancel" @click="handleClose">
            取消
          </view>
          <view class="evaluate-popup-btn-confirm" @click="handleConfirm">
            确定
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  max-height: 100vh;

  :deep(.textarea_bg) {
    //   @apply page bg-[#f0f0f0] color-amber; /* 使用 Tailwind 类 */

    /* 或者直接写 CSS */
    background: #f7f8fa !important;

    //   background-color: red !important;
    padding: 0.7rem !important;
    // border-radius: 0.5rem;
  }

  .evaluate-bar-remind {
    position: fixed;
    left: 0;
    right: 0;
    z-index: 999;
    padding: 30rpx 32rpx;
    background: #f2f9ff;
    color: rgba(0, 0, 0, 0.45);
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;

    .icon-remind {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }

  .evaluate-bar {
    position: fixed;
    left: 0;
    right: 0;
    z-index: 998;
    background-color: #fff;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;

    .evaluate-bar-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }

      .evaluate-bar-text {
        display: flex;
        flex-direction: column;

        .evaluate-bar-name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .evaluate-bar-meta {
          color: #999;
          font-size: 28rpx;
        }
      }
    }

    .evaluate-bar-action {
      background: #007fee;
      color: #fff;
      border-radius: 50rpx;
      padding: 12rpx 32rpx;
      font-size: 24rpx;
      font-family: 'PingFang SC';
    }
  }

  .evaluate-bar-placeholder {
    height: 88rpx;
    background: transparent;
  }

  .chat-page {
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
    flex: 1;

    // height: 0;
    .chat-list {
      flex: 1;
      overflow-y: auto;
      // padding: 24rpx;  // 移除
      .chat-list-inner {
        padding: 24rpx;
        padding-bottom: 120rpx; // 这里120rpx根据输入栏高度调整
        // padding-top 通过内联样式动态设置，避免被导航栏和评价栏遮挡
      }
      // height: 100%;
      .chat-item {
        display: flex;
        margin-bottom: 24rpx;

        &.user {
          flex-direction: row-reverse;

          .chat-content {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }

          .chat-bubble-row {
            display: flex;
            flex-direction: row;
            align-items: flex-end;
          }

          .chat-image {
            width: 240rpx;
            height: 240rpx;
            border-radius: 8rpx;
            margin: 8rpx 0;
          }

          .chat-bubble {
            background: #d0e9ff;
            color: #222;
            border-radius: 16rpx 0 16rpx 16rpx;
            padding: 16rpx;
            font-size: 30rpx;
            margin-bottom: 0;
            max-width: 60vw;
            word-break: break-all;
          }

          .chat-status-meta {
            margin-left: 16rpx;
            margin-right: 16rpx;
            font-size: 20rpx;
            color: #999;
            align-self: flex-end;
            white-space: nowrap;
          }
        }

        &.doctor {
          .chat-content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }

          .chat-image {
            width: 240rpx;
            height: 240rpx;
            border-radius: 8rpx;
            margin: 8rpx 0;
          }

          .chat-bubble-row {
            display: flex;
            flex-direction: row;
            align-items: flex-end;
          }

          .chat-bubble {
            background: #fff;
            color: #222;
            border-radius: 0 16rpx 16rpx 16rpx;
            padding: 16rpx;
            font-size: 30rpx;
            margin-bottom: 0;
            max-width: 60vw;
            word-break: break-all;
          }

          .chat-status-meta {
            margin-left: 8rpx;
            font-size: 20rpx;
            color: #999;
            align-self: flex-end;
            white-space: nowrap;
          }
        }

        .chat-avatar {
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          margin: 0 16rpx;
        }

        .chat-content {
          display: flex;
          flex-direction: column;
          max-width: 60vw;
        }
      }
    }

    .chat-input-bar {
      position: fixed;
      left: 0;
      right: 0;
      z-index: 10;
      // bottom 由绑定动态控制
      display: flex;
      align-items: center;
      padding: 24rpx 24rpx 50rpx 24rpx;
      background: #fff;
      border-top: 1px solid #eee;

      .icon-img {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .chat-input {
        flex: 1;
        border: none;
        background: #f5f5f5;
        border-radius: 32rpx;
        padding: 16rpx;
        font-size: 28rpx;
        margin-right: 16rpx;

        &.disabled {
          background: #e5e5e5;
          color: #999;
          cursor: not-allowed;
        }
      }

      .chat-send-btn {
        background: #007fee;
        color: #fff;
        border-radius: 32rpx;
        padding: 0 32rpx;
        font-size: 28rpx;

        &.disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
}

.chat-time {
  width: 100%;
  text-align: center;
  color: #bfbfbf;
  font-size: 22rpx;
  margin: 16rpx 0 8rpx 0;
}

.evaluate-popup {
  .evaluate-popup-title {
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 32rpx;
    font-style: normal;
    font-weight: 400;
    padding: 48rpx;
  }

  .evaluate-popup-content {
    display: flex;
    padding: 0 48rpx;

    .evaluate-popup-image {
      width: 104rpx;
      height: 104rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }

    .evaluate-popup-content-text {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .evaluate-popup-content-text-title {
        color: rgba(0, 0, 0, 0.85);
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }

  .evaluate-popup-textarea {
    padding: 0 48rpx;
    margin-top: 32rpx;

    ::v-deep .wd-textarea {
      background-color: #f7f8fa !important;
    }
  }

  .evaluate-popup-btn {
    display: flex;
    justify-content: space-between;
    padding: 0 48rpx;
    margin-bottom: 90rpx;
    margin-top: 72rpx;

    .evaluate-popup-btn-cancel {
      border-radius: 100px;
      border: 1px solid #bfbfbf;
      padding: 32rpx 48rpx;
      text-align: center;
      width: 214rpx;
      height: 32rpx;
    }

    .evaluate-popup-btn-confirm {
      border-radius: 100px;
      background: #3d7dff;
      color: #fff;
      text-align: center;
      width: 214rpx;
      height: 32rpx;
      padding: 32rpx 48rpx;
      gap: 20rpx;
    }
  }
}
::v-deep .wd-textarea__count {
  background: none !important;
}
</style>
