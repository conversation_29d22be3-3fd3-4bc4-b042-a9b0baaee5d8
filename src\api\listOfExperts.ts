import { http } from '@/utils/http'

// 查询专家信息列表左侧病种tab
export function getExpertLeftListAPI() {
  return http.get('/h5/disease/expert/listChronicDiseaseQ')
}

// 查询专家信息列表
export function getExpertrightListAPI(params) {
  return http.get('/h5/disease/expert/list', params)
}
// 获取专家详情
export function getExpertDetailAPI(id: string) {
  return http.get(`/h5/disease/expert/${id}`)
}
// 获取患者病史记录详细信息
export function getExpertRecordDetailAPI() {
  return http.get('/h5/disease/patientMedicalHistory')
}
// 验证是患者是否填写过记录
export function getExpertRecordAPI() {
  return http.get('/h5/disease/patientMedicalHistory/verifyExistence')
}
// 保存-编辑患者病史记录
export function saveExpertRecordAPI(data: any) {
  return http.post('/h5/disease/patientMedicalHistory', data)
}
// 查询患者评价列表
export function getExpertEvaluateAPI(params: any) {
  return http.get('/h5/disease/expert/listEvaluation', params)
}
// 智能匹配
export function getSmartMatchAPI(params: any) {
  return http.get('/h5/disease/expert/listStrategy', params)
}
