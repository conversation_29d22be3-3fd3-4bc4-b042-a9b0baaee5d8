<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import CustomTabbar from '@/components/custom-tabbar/index.vue'

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars">
    <slot />
    <CustomTabbar />
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>
